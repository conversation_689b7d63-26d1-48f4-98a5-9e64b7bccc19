import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getBrandsWithStats, BrandData } from '../services/newApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Search, 
  ArrowLeft, 
  Grid3X3, 
  Package, 
  Star, 
  ChevronRight,
  Building2,
  Smartphone,
  Laptop,
  Car,
  Gamepad2,
  Shirt
} from 'lucide-react';

const BrandsPage: React.FC = () => {
  const navigate = useNavigate();
  const [brands, setBrands] = useState<BrandData[]>([]);
  const [filteredBrands, setFilteredBrands] = useState<BrandData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');

  // Stats
  const [totalProducts, setTotalProducts] = useState(0);
  const [averageScore, setAverageScore] = useState(0);

  // Brand groups for filtering
  const brandGroups = [
    { title: 'Tech Giants', icon: <Smartphone className="h-4 w-4" />, brands: ['Apple', 'Samsung', 'Google', 'Microsoft', 'Sony'] },
    { title: 'Computing', icon: <Laptop className="h-4 w-4" />, brands: ['Dell', 'HP', 'Lenovo', 'ASUS', 'Acer'] },
    { title: 'Automotive', icon: <Car className="h-4 w-4" />, brands: ['Tesla', 'BMW', 'Mercedes', 'Toyota', 'Ford'] },
    { title: 'Gaming', icon: <Gamepad2 className="h-4 w-4" />, brands: ['Nintendo', 'PlayStation', 'Xbox', 'Razer', 'Logitech'] },
    { title: 'Fashion', icon: <Shirt className="h-4 w-4" />, brands: ['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo'] }
  ];

  // Fetch brands data
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const brandsData = await getBrandsWithStats();
        setBrands(brandsData);
        setFilteredBrands(brandsData);

        // Calculate totals
        const totalProds = brandsData.reduce((sum, brand) => sum + brand.productCount, 0);
        const avgScore = brandsData.length > 0 
          ? brandsData.reduce((sum, brand) => sum + brand.averageScore, 0) / brandsData.length 
          : 0;

        setTotalProducts(totalProds);
        setAverageScore(avgScore);
      } catch (err) {
        console.error('Error fetching brands:', err);
        setError('Failed to load brands. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, []);

  // Filter brands based on search and group
  useEffect(() => {
    let filtered = brands;

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(brand =>
        brand.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by group
    if (selectedGroup !== 'all') {
      const group = brandGroups.find(g => g.title === selectedGroup);
      if (group) {
        filtered = filtered.filter(brand =>
          group.brands.some(groupBrand => 
            brand.name.toLowerCase().includes(groupBrand.toLowerCase())
          )
        );
      }
    }

    setFilteredBrands(filtered);
  }, [searchTerm, selectedGroup, brands]);

  const handleBrandClick = (brandName: string) => {
    navigate(`/search?brand=${encodeURIComponent(brandName)}`);
  };

  const getBrandIcon = (brandName: string) => {
    const name = brandName.toLowerCase();
    if (['apple', 'samsung', 'google', 'microsoft', 'sony'].some(tech => name.includes(tech))) {
      return <Smartphone className="h-5 w-5 text-blue-600" />;
    }
    if (['dell', 'hp', 'lenovo', 'asus', 'acer'].some(comp => name.includes(comp))) {
      return <Laptop className="h-5 w-5 text-purple-600" />;
    }
    if (['tesla', 'bmw', 'mercedes', 'toyota', 'ford'].some(auto => name.includes(auto))) {
      return <Car className="h-5 w-5 text-red-600" />;
    }
    if (['nintendo', 'playstation', 'xbox', 'razer', 'logitech'].some(game => name.includes(game))) {
      return <Gamepad2 className="h-5 w-5 text-green-600" />;
    }
    if (['nike', 'adidas', 'zara', 'h&m', 'uniqlo'].some(fashion => name.includes(fashion))) {
      return <Shirt className="h-5 w-5 text-pink-600" />;
    }
    return <Building2 className="h-5 w-5 text-gray-600" />;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-8 sm:py-12 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight">
              Explore All Brands
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 leading-relaxed">
              Discover products organized by brand to find exactly what you're looking for
            </p>

            {/* Search Bar */}
            <div className="relative max-w-sm sm:max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5" />
              <Input
                type="text"
                placeholder="Search brands..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 sm:pl-10 pr-4 py-2 sm:py-3 text-base sm:text-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Bar */}
      <section className="py-4 sm:py-6 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4 sm:gap-6 lg:gap-8 text-center">
            <div className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="font-semibold text-sm sm:text-base">
                {brands.length}
              </span>
              <span className="text-gray-600 text-sm sm:text-base">Brands</span>
            </div>
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="font-semibold text-sm sm:text-base">
                {totalProducts.toLocaleString()}
              </span>
              <span className="text-gray-600 text-sm sm:text-base">Products</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="font-semibold text-sm sm:text-base">
                {averageScore.toFixed(1)}
              </span>
              <span className="text-gray-600 text-sm sm:text-base">Avg Score</span>
            </div>
          </div>
        </div>
      </section>

      {/* Brand Groups Filter */}
      <section className="py-4 sm:py-6 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-2 sm:gap-3 justify-center">
            <Button
              variant={selectedGroup === "all" ? "default" : "outline"}
              onClick={() => setSelectedGroup("all")}
              className="rounded-full text-xs sm:text-sm"
              size="sm"
            >
              All Brands
            </Button>
            {brandGroups.map((group) => (
              <Button
                key={group.title}
                variant={selectedGroup === group.title ? "default" : "outline"}
                onClick={() => setSelectedGroup(group.title)}
                className="rounded-full flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
                size="sm"
              >
                <span className="hidden sm:inline">{group.icon}</span>
                {group.title}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Brands Grid */}
      <section className="py-8 sm:py-12">
        <div className="container mx-auto px-4">
          {error && (
            <div className="text-center py-8">
              <p className="text-red-600 text-sm sm:text-base">{error}</p>
            </div>
          )}

          {!error && !loading && (
            <>
              {filteredBrands.length === 0 && (
                <div className="text-center py-8 sm:py-12">
                  <Search className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-base sm:text-lg font-medium text-gray-700 mb-2">No brands found</p>
                  <p className="text-gray-600 text-sm sm:text-base">Try adjusting your search or filter.</p>
                </div>
              )}

              {filteredBrands.length > 0 && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                  {filteredBrands.map((brand) => (
                    <BrandCard
                      key={brand.name}
                      brand={brand}
                      onClick={() => handleBrandClick(brand.name)}
                      icon={getBrandIcon(brand.name)}
                    />
                  ))}
                </div>
              )}
            </>
          )}

          {/* Loading state */}
          {loading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} className="border-gray-200">
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-5 w-5 sm:h-6 sm:w-6 rounded" />
                      <Skeleton className="h-4 w-20 sm:h-5 sm:w-24" />
                    </div>
                    <div className="flex items-center justify-between mb-4">
                      <Skeleton className="h-3 w-16 sm:h-4 sm:w-20" />
                      <Skeleton className="h-4 w-12 sm:h-5 sm:w-16" />
                    </div>
                    <div className="flex -space-x-2">
                      <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full" />
                      <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full" />
                      <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

// Brand Card Component
const BrandCard = ({ 
  brand, 
  onClick, 
  icon 
}: { 
  brand: BrandData; 
  onClick: () => void;
  icon: React.ReactNode;
}) => {
  return (
    <Card
      className="cursor-pointer group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200 hover:border-primary/30"
      onClick={onClick}
    >
      <CardContent className="p-4 sm:p-6">
        {/* Icon and Title */}
        <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
          <div className="flex-shrink-0">
            {icon}
          </div>
          <h3 className="font-semibold text-sm sm:text-base text-gray-900 group-hover:text-primary transition-colors line-clamp-2 flex-1 leading-tight">
            {brand.name}
          </h3>
          <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors flex-shrink-0" />
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center gap-1 text-xs sm:text-sm text-gray-600">
            <Package className="h-3 w-3 sm:h-4 sm:w-4" />
            <span>{brand.productCount} products</span>
          </div>
          {brand.averageScore > 0 && (
            <Badge variant="secondary" className="text-xs">
              {brand.averageScore}% score
            </Badge>
          )}
        </div>

        {/* Top Products Preview */}
        {brand.topProducts.length > 0 && (
          <div className="flex -space-x-1 sm:-space-x-2">
            {brand.topProducts.map((product, index) => (
              <div
                key={product.id}
                className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-white bg-gray-100 overflow-hidden"
                style={{ zIndex: brand.topProducts.length - index }}
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-product.png';
                  }}
                />
              </div>
            ))}
            {brand.productCount > 3 && (
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 border-white bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600">
                +{brand.productCount - 3}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BrandsPage;
