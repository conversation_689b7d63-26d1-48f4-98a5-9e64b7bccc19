import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Search, Package, Star, TrendingUp, Grid3X3, ArrowLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import Header from "@/components/Header";
import { getCategories, getProducts, getSubcategories } from "@/services/newApi";
import { getCategoryIcon } from "@/components/ProductCategoriesGrid";

interface CategoryData {
  name: string;
  icon: React.ReactNode;
  productCount: number;
  averageScore: number;
  topProducts: Array<{
    id: string;
    name: string;
    image: string;
  }>;
}

interface SubcategoryData {
  name: string;
  productCount: number;
  averageScore: number;
  topProducts: Array<{
    id: string;
    name: string;
    image: string;
  }>;
}

interface CategoryGroup {
  title: string;
  categories: string[];
  icon: React.ReactNode;
  color: string;
}

const categoryGroups: CategoryGroup[] = [
  {
    title: "Beauty & Skincare",
    categories: ["Beauty & Skincare"],
    icon: <Star className="h-6 w-6" />,
    color: "bg-pink-50 border-pink-200 text-pink-700"
  },
  {
    title: "Home & Kitchen Gadgets",
    categories: ["Home & Kitchen Gadgets"],
    icon: <Grid3X3 className="h-6 w-6" />,
    color: "bg-green-50 border-green-200 text-green-700"
  },
  {
    title: "Electronics",
    categories: ["Electronics"],
    icon: <Package className="h-6 w-6" />,
    color: "bg-blue-50 border-blue-200 text-blue-700"
  },
  {
    title: "Fashion & Apparel",
    categories: ["Fashion & Apparel"],
    icon: <TrendingUp className="h-6 w-6" />,
    color: "bg-purple-50 border-purple-200 text-purple-700"
  },
  {
    title: "Watches, Jewelry & Footwear",
    categories: ["Watches, Jewelry & Footwear"],
    icon: <Star className="h-6 w-6" />,
    color: "bg-orange-50 border-orange-200 text-orange-700"
  }
];

const CategoriesPage = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<CategoryData[]>([]);
  const [subcategories, setSubcategories] = useState<SubcategoryData[]>([]);
  const [filteredSubcategories, setFilteredSubcategories] = useState<SubcategoryData[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string>("all");

  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all categories
        const categoryNames = await getCategories();

        // Fetch products for each category to get counts and top products
        const categoryDataPromises = categoryNames.map(async (categoryName: string) => {
          try {
            const products = await getProducts({ category: categoryName, limit: 3 });

            const averageScore = products.data.length > 0
              ? products.data.reduce((sum: number, product: any) => sum + product.score, 0) / products.data.length
              : 0;

            return {
              name: categoryName,
              icon: getCategoryIcon(categoryName),
              productCount: products.total || 0,
              averageScore: Math.round(averageScore),
              topProducts: products.data.slice(0, 3).map((product: any) => ({
                id: product._id,
                name: product.name,
                image: product.image
              }))
            };
          } catch (err) {
            console.error(`Error fetching data for category ${categoryName}:`, err);
            return {
              name: categoryName,
              icon: getCategoryIcon(categoryName),
              productCount: 0,
              averageScore: 0,
              topProducts: []
            };
          }
        });

        const categoryData = await Promise.all(categoryDataPromises);
        setCategories(categoryData);
        setFilteredCategories(categoryData);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryData();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      // Filter subcategories
      let filtered = subcategories;

      if (searchTerm) {
        filtered = filtered.filter(subcategory =>
          subcategory.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      setFilteredSubcategories(filtered);
    } else {
      // Filter categories
      let filtered = categories;

      // Filter by search term
      if (searchTerm) {
        filtered = filtered.filter(category =>
          category.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // Filter by selected group
      if (selectedGroup !== "all") {
        const group = categoryGroups.find(g => g.title === selectedGroup);
        if (group) {
          filtered = filtered.filter(category =>
            group.categories.includes(category.name)
          );
        }
      }

      setFilteredCategories(filtered);
    }
  }, [searchTerm, selectedGroup, categories, subcategories, selectedCategory]);

  const handleCategoryClick = async (categoryName: string) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedCategory(categoryName);
      setSearchTerm(""); // Clear search when navigating to subcategories

      // Fetch subcategories for the selected category
      const subcategoryNames = await getSubcategories(categoryName);

      // Fetch products for each subcategory to get counts and top products
      const subcategoryDataPromises = subcategoryNames.map(async (subcategoryName: string) => {
        try {
          const products = await getProducts({
            category: categoryName,
            subcategory: subcategoryName,
            limit: 3
          });

          const averageScore = products.data.length > 0
            ? products.data.reduce((sum: number, product: any) => sum + product.score, 0) / products.data.length
            : 0;

          return {
            name: subcategoryName,
            productCount: products.total || 0,
            averageScore: Math.round(averageScore),
            topProducts: products.data.slice(0, 3).map((product: any) => ({
              id: product._id,
              name: product.name,
              image: product.image
            }))
          };
        } catch (err) {
          console.error(`Error fetching data for subcategory ${subcategoryName}:`, err);
          return {
            name: subcategoryName,
            productCount: 0,
            averageScore: 0,
            topProducts: []
          };
        }
      });

      const subcategoryData = await Promise.all(subcategoryDataPromises);
      setSubcategories(subcategoryData);
      setFilteredSubcategories(subcategoryData);
    } catch (err) {
      console.error('Error fetching subcategories:', err);
      setError('Failed to load subcategories');
    } finally {
      setLoading(false);
    }
  };

  const handleSubcategoryClick = (subcategoryName: string) => {
    if (selectedCategory) {
      navigate(`/search?category=${encodeURIComponent(selectedCategory)}&subcategory=${encodeURIComponent(subcategoryName)}`);
    }
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setSubcategories([]);
    setFilteredSubcategories([]);
    setSearchTerm("");
  };

  const totalProducts = categories.reduce((sum, cat) => sum + cat.productCount, 0);
  const averageScore = categories.length > 0
    ? categories.reduce((sum, cat) => sum + cat.averageScore, 0) / categories.length
    : 0;

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array.from({ length: 12 }).map((_, i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
              Home
            </Button>
            <span className="mx-2">/</span>
            {selectedCategory ? (
              <>
                <Button variant="link" className="p-0 h-auto" onClick={handleBackToCategories}>
                  Categories
                </Button>
                <span className="mx-2">/</span>
                <span className="text-gray-900 font-medium">{selectedCategory}</span>
              </>
            ) : (
              <span className="text-gray-900 font-medium">Categories</span>
            )}
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            {selectedCategory && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToCategories}
                className="mb-4 text-primary hover:text-primary/80"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Categories
              </Button>
            )}
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {selectedCategory ? `${selectedCategory} Subcategories` : 'Explore All Categories'}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              {selectedCategory
                ? `Discover specific product types within ${selectedCategory}`
                : 'Discover products across all categories to find exactly what you\'re looking for'
              }
            </p>

            {/* Search Bar */}
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder={selectedCategory ? "Search subcategories..." : "Search categories..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-3 text-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Bar */}
      <section className="py-6 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-8 text-center">
            <div className="flex items-center gap-2">
              <Grid3X3 className="h-5 w-5 text-primary" />
              <span className="font-semibold">
                {selectedCategory ? subcategories.length : categories.length}
              </span>
              <span className="text-gray-600">
                {selectedCategory ? 'Subcategories' : 'Categories'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-primary" />
              <span className="font-semibold">
                {selectedCategory
                  ? subcategories.reduce((sum, sub) => sum + sub.productCount, 0).toLocaleString()
                  : totalProducts.toLocaleString()
                }
              </span>
              <span className="text-gray-600">Products</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              <span className="font-semibold">
                {selectedCategory
                  ? subcategories.length > 0
                    ? (subcategories.reduce((sum, sub) => sum + sub.averageScore, 0) / subcategories.length).toFixed(1)
                    : '0.0'
                  : averageScore.toFixed(1)
                }
              </span>
              <span className="text-gray-600">Avg Score</span>
            </div>
          </div>
        </div>
      </section>

      {/* Category Groups Filter - Only show when viewing categories */}
      {!selectedCategory && (
        <section className="py-6 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap gap-3 justify-center">
              <Button
                variant={selectedGroup === "all" ? "default" : "outline"}
                onClick={() => setSelectedGroup("all")}
                className="rounded-full"
              >
                All Categories
              </Button>
              {categoryGroups.map((group) => (
                <Button
                  key={group.title}
                  variant={selectedGroup === group.title ? "default" : "outline"}
                  onClick={() => setSelectedGroup(group.title)}
                  className="rounded-full flex items-center gap-2"
                >
                  {group.icon}
                  {group.title}
                </Button>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Categories/Subcategories Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {!error && !loading && (
            <>
              {/* Show categories */}
              {!selectedCategory && filteredCategories.length === 0 && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-700 mb-2">No categories found</p>
                  <p className="text-gray-600">Try adjusting your search or filter.</p>
                </div>
              )}

              {!selectedCategory && (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {filteredCategories.map((category) => (
                    <CategoryCard
                      key={category.name}
                      category={category}
                      onClick={() => handleCategoryClick(category.name)}
                    />
                  ))}
                </div>
              )}

              {/* Show subcategories */}
              {selectedCategory && filteredSubcategories.length === 0 && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-700 mb-2">No subcategories found</p>
                  <p className="text-gray-600">Try adjusting your search.</p>
                </div>
              )}

              {selectedCategory && (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {filteredSubcategories.map((subcategory) => (
                    <SubcategoryCard
                      key={subcategory.name}
                      subcategory={subcategory}
                      onClick={() => handleSubcategoryClick(subcategory.name)}
                    />
                  ))}
                </div>
              )}
            </>
          )}

          {/* Loading state */}
          {loading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} className="border-gray-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-6 w-6 rounded" />
                      <Skeleton className="h-5 w-24" />
                    </div>
                    <div className="flex items-center justify-between mb-4">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <div className="flex -space-x-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <Skeleton className="h-8 w-8 rounded-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

// Category Card Component
const CategoryCard = ({ category, onClick }: { category: CategoryData; onClick: () => void }) => {
  return (
    <Card
      className="cursor-pointer group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200 hover:border-primary/30"
      onClick={onClick}
    >
      <CardContent className="p-6">
        {/* Icon and Title */}
        <div className="flex items-center gap-3 mb-4">
          <div className="text-primary group-hover:text-primary/80 transition-colors">
            {category.icon}
          </div>
          <h3 className="font-semibold text-gray-900 group-hover:text-primary transition-colors line-clamp-2">
            {category.name}
          </h3>
          <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors ml-auto" />
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Package className="h-4 w-4" />
            <span>{category.productCount} products</span>
          </div>
          {category.averageScore > 0 && (
            <Badge variant="secondary" className="text-xs">
              {category.averageScore}% score
            </Badge>
          )}
        </div>

        {/* Top Products Preview */}
        {category.topProducts.length > 0 && (
          <div className="flex -space-x-2">
            {category.topProducts.map((product, index) => (
              <div
                key={product.id}
                className="w-8 h-8 rounded-full border-2 border-white bg-gray-100 overflow-hidden"
                style={{ zIndex: category.topProducts.length - index }}
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-product.png';
                  }}
                />
              </div>
            ))}
            {category.productCount > 3 && (
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600">
                +{category.productCount - 3}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Subcategory Card Component
const SubcategoryCard = ({ subcategory, onClick }: { subcategory: SubcategoryData; onClick: () => void }) => {
  return (
    <Card
      className="cursor-pointer group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200 hover:border-primary/30"
      onClick={onClick}
    >
      <CardContent className="p-6">
        {/* Title */}
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <Package className="h-5 w-5 text-primary" />
          </div>
          <h3 className="font-semibold text-gray-900 group-hover:text-primary transition-colors line-clamp-2 flex-1">
            {subcategory.name}
          </h3>
          <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Package className="h-4 w-4" />
            <span>{subcategory.productCount} products</span>
          </div>
          {subcategory.averageScore > 0 && (
            <Badge variant="secondary" className="text-xs">
              {subcategory.averageScore}% score
            </Badge>
          )}
        </div>

        {/* Top Products Preview */}
        {subcategory.topProducts.length > 0 && (
          <div className="flex -space-x-2">
            {subcategory.topProducts.map((product, index) => (
              <div
                key={product.id}
                className="w-8 h-8 rounded-full border-2 border-white bg-gray-100 overflow-hidden"
                style={{ zIndex: subcategory.topProducts.length - index }}
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-product.png';
                  }}
                />
              </div>
            ))}
            {subcategory.productCount > 3 && (
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600">
                +{subcategory.productCount - 3}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategoriesPage;
