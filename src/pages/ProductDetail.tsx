import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getProductBySlug, clearProductCache } from '../services/newApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, ExternalLink, Loader2, ThumbsUp, ThumbsDown } from 'lucide-react';
import Header from '@/components/Header';

const ProductDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!slug) return;

    const fetchProduct = async () => {
      setLoading(true);
      setError(null);

      try {
        // Try to fetch with force refresh to avoid stale cache
        const data = await getProductBySlug(slug, true);
        setProduct(data);
      } catch (err) {
        // If there's an error, clear the product cache to prevent future issues
        clearProductCache();
        setError(err instanceof Error ? err.message : 'Failed to fetch product details');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [slug]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    if (score >= 50) return 'bg-yellow-100 text-yellow-800';
    if (score >= 40) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-12 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error || 'Product not found'}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-8">

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-8">
          <div>
            <div className="rounded-lg overflow-hidden border bg-white p-4 flex items-center justify-center h-[300px] sm:h-[400px]">
              <img
                src={product.image}
                alt={product.name}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          </div>

          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-3 leading-tight">{product.name}</h1>

            <div className="flex flex-wrap items-center gap-2 mb-4">
              <Badge className="text-xs sm:text-sm">{product.category}</Badge>
              <Badge variant="outline" className="text-xs sm:text-sm">{product.brand}</Badge>
              <Badge
                className={`${getScoreColor(product.score)} text-xs sm:text-sm`}
              >
                {product.score}%
              </Badge>
            </div>

            {product.price > 0 && (
              <p className="text-xl sm:text-2xl font-bold mb-4">${product.price.toFixed(2)}</p>
            )}

            <p className="text-gray-700 mb-6 text-sm sm:text-base leading-relaxed">{product.description}</p>

            {product.affiliateLink && (
              <Button className="w-full mb-6" asChild>
                <a href={product.affiliateLink} target="_blank" rel="noopener noreferrer">
                  Check it out <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
            )}

            {/* Pros and Cons - Stacked vertically */}
            <div className="space-y-4 mb-6">
              {/* Pros Card */}
              <Card className="border-green-100 bg-green-50/30">
                <CardHeader className="p-4 pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center text-green-700">
                    <ThumbsUp className="mr-2 h-5 w-5 text-green-600" /> Pros
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  {product.pros.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-2">
                      {product.pros.map((pro: string, index: number) => (
                        <li key={index} className="text-sm sm:text-base text-gray-700 leading-relaxed">{pro}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No pros listed</p>
                  )}
                </CardContent>
              </Card>

              {/* Cons Card */}
              <Card className="border-red-100 bg-red-50/30">
                <CardHeader className="p-4 pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center text-red-700">
                    <ThumbsDown className="mr-2 h-5 w-5 text-red-600" /> Cons
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  {product.cons.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-2">
                      {product.cons.map((con: string, index: number) => (
                        <li key={index} className="text-sm sm:text-base text-gray-700 leading-relaxed">{con}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No cons listed</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <Tabs defaultValue="opinion" className="mt-8">
          <TabsList>
            <TabsTrigger value="opinion">Opinion</TabsTrigger>
          </TabsList>

          <TabsContent value="opinion" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Our Opinion</CardTitle>
                <CardDescription>
                  Detailed review and thoughts about this product
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p>{product.opinion}</p>
                </div>

                <Separator className="my-6" />

                <div>
                  <h3 className="text-lg font-semibold mb-2">Score</h3>
                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(product.score)}`}>
                    {product.score}%
                  </div>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {product.score >= 80 ? 'Excellent' :
                      product.score >= 70 ? 'Very Good' :
                        product.score >= 60 ? 'Good' :
                          product.score >= 50 ? 'Average' :
                            product.score >= 40 ? 'Below Average' : 'Poor'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ProductDetail;
