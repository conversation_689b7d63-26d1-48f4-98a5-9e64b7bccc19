import { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "./components/ErrorBoundary";

// Lazy-loaded components for code splitting
const Home = lazy(() => import("./components/home"));
const NewSearchResults = lazy(() => import("./components/NewSearchResults"));
const ProductDetailPage = lazy(() => import("./pages/product/[id]"));
// NewProductDetailPage removed - using slug routes only
const AdminPage = lazy(() => import("./pages/AdminPage"));
// ProductList removed as requested - using categories page instead
const ProductDetail = lazy(() => import("./pages/ProductDetail"));
const CategoriesPage = lazy(() => import("./pages/CategoriesPage"));
const BrandsPage = lazy(() => import("./pages/BrandsPage"));

// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
);

function App() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingFallback />}>
        {/* Main application routes */}
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/search" element={<NewSearchResults />} />
          <Route path="/categories" element={<CategoriesPage />} />
          <Route path="/brands" element={<BrandsPage />} />
          {/* /products/id/ route removed - using slug routes only */}
          {/* ProductList route removed - using categories page instead */}
          <Route path="/products/:slug" element={<ProductDetail />} />
          <Route path="/admin" element={<AdminPage />} />
        </Routes>
        {/* Tempo routes removed for production */}
      </Suspense>

      {/* Toast notifications */}
      <Toaster />
    </ErrorBoundary>
  );
}

export default App;
