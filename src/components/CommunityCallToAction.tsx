import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Star, Users, MessageCircle, TrendingUp } from "lucide-react";

const CommunityCallToAction = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-orange-50 to-amber-50">
      <div className="container mx-auto px-4">
        <Card className="bg-gradient-to-r from-orange-100 to-amber-100 border-orange-200 shadow-lg overflow-hidden">
          <div className="p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Left side - Content */}
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  Read unbiased reviews to make informed decisions
                </h2>
                <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                  Navigate options with confidence and clarity.
                  Join discussions and share knowledge with fellow enthusiasts.
                  Your voice matters – make a difference with your feedback.
                </p>

                {/* Stats */}
                <div className="flex flex-wrap gap-6 mb-8">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-orange-600" />
                    <span className="text-sm font-medium text-gray-700">10K+ Users</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-orange-600" />
                    <span className="text-sm font-medium text-gray-700">50K+ Opinions</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-orange-600" />
                    <span className="text-sm font-medium text-gray-700">Growing Daily</span>
                  </div>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    size="lg"
                    className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-full font-medium"
                  >
                    Share Your Opinion
                  </Button>

                  {/* Social login options
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-600">or continue with</span>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-gray-300 hover:bg-white"
                      >
                        <svg className="h-4 w-4" viewBox="0 0 24 24">
                          <path
                            fill="currentColor"
                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          />
                          <path
                            fill="currentColor"
                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          />
                        </svg>
                      </Button>

                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-gray-300 hover:bg-white"
                      >
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                      </Button>

                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-gray-300 hover:bg-white"
                      >
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                      </Button>
                    </div>
                  </div> */}
                </div>
              </div>

              {/* Right side - Images */}
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  {/* Image 1 - Person with laptop */}
                  <div className="relative">
                    <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600 p-6 flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Star className="h-8 w-8" />
                        </div>
                        <p className="text-sm font-medium">Product Reviews</p>
                      </div>
                    </div>
                  </div>

                  {/* Image 2 - Shopping comparison */}
                  <div className="relative mt-8">
                    <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-green-400 to-emerald-600 p-6 flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                          <TrendingUp className="h-8 w-8" />
                        </div>
                        <p className="text-sm font-medium">Smart Choices</p>
                      </div>
                    </div>
                  </div>

                  {/* Image 3 - Community */}
                  <div className="relative -mt-4">
                    <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-purple-400 to-purple-600 p-6 flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Users className="h-8 w-8" />
                        </div>
                        <p className="text-sm font-medium">Community</p>
                      </div>
                    </div>
                  </div>

                  {/* Image 4 - Feedback */}
                  <div className="relative">
                    <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-orange-400 to-red-500 p-6 flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                          <MessageCircle className="h-8 w-8" />
                        </div>
                        <p className="text-sm font-medium">Share Opinions</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating elements for visual interest */}
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-pulse"></div>
                <div className="absolute bottom-4 -left-2 w-4 h-4 bg-pink-400 rounded-full animate-bounce"></div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default CommunityCallToAction;
