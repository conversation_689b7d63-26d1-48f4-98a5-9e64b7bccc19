import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ointer, <PERSON> } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ProductClick {
  _id: string;
  productId: string;
  productName: string;
  productSlug: string;
  clickCount: number;
  lastClicked: string;
  category?: string;
  brand?: string;
}

interface AnalyticsData {
  totalClicks: number;
  totalProducts: number;
  averageClicksPerProduct: number;
  topProducts: ProductClick[];
  recentClicks: ProductClick[];
}

const ProductClickAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchAnalytics = async () => {
    try {
      setError(null);
      
      // For now, we'll use mock data since the analytics API might not be implemented yet
      // In a real implementation, this would call an API endpoint like:
      // const response = await fetch('/api/admin/analytics/product-clicks');
      // const data = await response.json();
      
      // Mock data for demonstration
      const mockData: AnalyticsData = {
        totalClicks: 1247,
        totalProducts: 156,
        averageClicksPerProduct: 8.0,
        topProducts: [
          {
            _id: '1',
            productId: 'prod_1',
            productName: 'iPhone 15 Pro Max',
            productSlug: 'iphone-15-pro-max',
            clickCount: 89,
            lastClicked: '2024-01-15T10:30:00Z',
            category: 'Electronics',
            brand: 'Apple'
          },
          {
            _id: '2',
            productId: 'prod_2',
            productName: 'MacBook Pro M3',
            productSlug: 'macbook-pro-m3',
            clickCount: 76,
            lastClicked: '2024-01-15T09:45:00Z',
            category: 'Electronics',
            brand: 'Apple'
          },
          {
            _id: '3',
            productId: 'prod_3',
            productName: 'Sony WH-1000XM5',
            productSlug: 'sony-wh-1000xm5',
            clickCount: 64,
            lastClicked: '2024-01-15T08:20:00Z',
            category: 'Electronics',
            brand: 'Sony'
          },
          {
            _id: '4',
            productId: 'prod_4',
            productName: 'Samsung Galaxy S24 Ultra',
            productSlug: 'samsung-galaxy-s24-ultra',
            clickCount: 52,
            lastClicked: '2024-01-14T16:15:00Z',
            category: 'Electronics',
            brand: 'Samsung'
          },
          {
            _id: '5',
            productId: 'prod_5',
            productName: 'AirPods Pro 2',
            productSlug: 'airpods-pro-2',
            clickCount: 48,
            lastClicked: '2024-01-14T14:30:00Z',
            category: 'Electronics',
            brand: 'Apple'
          }
        ],
        recentClicks: [
          {
            _id: '6',
            productId: 'prod_6',
            productName: 'Tesla Model Y',
            productSlug: 'tesla-model-y',
            clickCount: 3,
            lastClicked: '2024-01-15T11:45:00Z',
            category: 'Automotive',
            brand: 'Tesla'
          },
          {
            _id: '7',
            productId: 'prod_7',
            productName: 'Nintendo Switch OLED',
            productSlug: 'nintendo-switch-oled',
            clickCount: 12,
            lastClicked: '2024-01-15T11:30:00Z',
            category: 'Gaming',
            brand: 'Nintendo'
          }
        ]
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalyticsData(mockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAnalytics();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getClickBadgeColor = (clickCount: number) => {
    if (clickCount >= 50) return 'bg-green-100 text-green-800';
    if (clickCount >= 20) return 'bg-blue-100 text-blue-800';
    if (clickCount >= 10) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Product Click Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Product Click Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={handleRefresh} className="mt-4">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!analyticsData) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.totalClicks.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across all products
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products Tracked</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Products with click data
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Clicks/Product</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.averageClicksPerProduct.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Average engagement
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Products Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Top Performing Products</CardTitle>
              <CardDescription>
                Products with the highest click counts
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              {refreshing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <TrendingUp className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableCaption>Top products by click count</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Brand</TableHead>
                <TableHead>Clicks</TableHead>
                <TableHead>Last Clicked</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {analyticsData.topProducts.map((product, index) => (
                <TableRow key={product._id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <span className="mr-2 text-sm text-muted-foreground">#{index + 1}</span>
                      {product.productName}
                    </div>
                  </TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell>{product.brand}</TableCell>
                  <TableCell>
                    <Badge className={getClickBadgeColor(product.clickCount)}>
                      {product.clickCount} clicks
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(product.lastClicked)}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductClickAnalytics;
