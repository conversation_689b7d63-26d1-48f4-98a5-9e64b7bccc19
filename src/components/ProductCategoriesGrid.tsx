import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Smartphone,
  Laptop,
  Headphones,
  Watch,
  Camera,
  Gamepad2,
  Home,
  Car,
  Shirt,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Book,
  ChevronLeft,
  ChevronRight,
  Monitor,
  Sofa,
  Utensils,
  Bed,
  Palette,
  Sparkles,
  Heart,
  Music,
  Briefcase,
  PawPrint,
  ShoppingBag,
  Hammer,
  TreePine,
  Plane,
  Wrench,
  Package
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { getCategories } from "../services/newApi";

interface Category {
  id: string;
  name: string;
  icon: React.ReactNode;
}

// Function to get appropriate icon for a category
export const getCategoryIcon = (categoryName: string): React.ReactNode => {
  const name = categoryName.toLowerCase();

  // New category mappings for the 5 core categories
  if (name === 'beauty & skincare') return <Sparkles className="h-8 w-8" />;
  if (name === 'home & kitchen gadgets') return <Home className="h-8 w-8" />;
  if (name === 'electronics') return <Smartphone className="h-8 w-8" />;
  if (name === 'fashion & apparel') return <Shirt className="h-8 w-8" />;
  if (name === 'watches, jewelry & footwear') return <Watch className="h-8 w-8" />;

  // Fallback for subcategories or other categories
  if (name.includes('beauty') || name.includes('makeup') || name.includes('skincare') || name.includes('hair') || name.includes('fragrance')) return <Sparkles className="h-8 w-8" />;
  if (name.includes('home') || name.includes('kitchen')) return <Home className="h-8 w-8" />;
  if (name.includes('electronics') || name.includes('headphone') || name.includes('speaker') || name.includes('tablet') || name.includes('gaming')) return <Smartphone className="h-8 w-8" />;
  if (name.includes('fashion') || name.includes('clothing') || name.includes('apparel') || name.includes('men') || name.includes('women') || name.includes('dress')) return <Shirt className="h-8 w-8" />;
  if (name.includes('watch') || name.includes('jewelry') || name.includes('footwear') || name.includes('shoes')) return <Watch className="h-8 w-8" />;

  // Default icon
  return <Package className="h-8 w-8" />;
};

const ProductCategoriesGrid: React.FC = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories from backend on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);

        const categoryNames = await getCategories();

        // Transform backend data to component format
        const transformedCategories: Category[] = categoryNames.map((name) => ({
          id: name,
          name: name,
          icon: getCategoryIcon(name)
        }));

        setCategories(transformedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryClick = (categoryName: string) => {
    navigate(`/search?category=${encodeURIComponent(categoryName)}`);
  };

  const handleSeeMore = () => {
    navigate('/categories');
  };

  // Show loading state
  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading categories...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Don't render if no categories
  if (categories.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
            What are you looking for?
          </h2>

          <div className="flex items-center gap-2">
            {/* Navigation arrows */}
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full border-gray-300 hover:bg-gray-50"
              onClick={() => {
                const container = document.getElementById('categories-grid');
                if (container) {
                  container.scrollBy({ left: -300, behavior: 'smooth' });
                }
              }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full border-gray-300 hover:bg-gray-50"
              onClick={() => {
                const container = document.getElementById('categories-grid');
                if (container) {
                  container.scrollBy({ left: 300, behavior: 'smooth' });
                }
              }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* See more button */}
            <Button
              variant="outline"
              className="ml-2 text-blue-600 border-blue-600 hover:bg-blue-50"
              onClick={handleSeeMore}
            >
              See more
            </Button>
          </div>
        </div>

        {/* Categories Grid */}
        <div
          id="categories-grid"
          className="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {categories.map((category) => (
            <div
              key={category.id}
              onClick={() => handleCategoryClick(category.name)}
              className="flex-shrink-0 w-40 cursor-pointer group"
            >
              <div className="flex flex-col items-center text-center p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                {/* Icon */}
                <div className="mb-3 text-gray-600 group-hover:text-blue-600 transition-colors duration-200">
                  {category.icon}
                </div>

                {/* Category Name */}
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                  {category.name}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile See More Button */}
        <div className="mt-6 text-center md:hidden">
          <Button
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleSeeMore}
          >
            See all categories
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProductCategoriesGrid;
