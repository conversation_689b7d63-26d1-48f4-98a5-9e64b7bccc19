import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Smartphone,
  Laptop,
  Headphones,
  Watch,
  Camera,
  Gamepad2,
  Home,
  Car,
  Shirt,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Book,
  ChevronLeft,
  ChevronRight,
  Monitor,
  Sofa,
  Utensils,
  Bed,
  Palette,
  Sparkles,
  Heart,
  Music,
  Briefcase,
  PawPrint,
  ShoppingBag,
  Hammer,
  TreePine,
  Plane,
  Wrench,
  Package,
  Building2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getBrands } from "../services/newApi";

interface Brand {
  id: string;
  name: string;
  icon: React.ReactNode;
}

// Function to get appropriate icon for a brand
export const getBrandIcon = (brandName: string): React.ReactNode => {
  const name = brandName.toLowerCase();

  // Brand-specific icon mappings
  // Tech Giants
  if (name.includes('apple')) return <Smartphone className="h-8 w-8" />;
  if (name.includes('samsung')) return <Smartphone className="h-8 w-8" />;
  if (name.includes('google')) return <Smartphone className="h-8 w-8" />;
  if (name.includes('microsoft')) return <Laptop className="h-8 w-8" />;
  if (name.includes('sony')) return <Camera className="h-8 w-8" />;

  // Computing
  if (name.includes('dell')) return <Laptop className="h-8 w-8" />;
  if (name.includes('hp')) return <Laptop className="h-8 w-8" />;
  if (name.includes('lenovo')) return <Laptop className="h-8 w-8" />;
  if (name.includes('asus')) return <Laptop className="h-8 w-8" />;
  if (name.includes('acer')) return <Laptop className="h-8 w-8" />;

  // Automotive
  if (name.includes('tesla')) return <Car className="h-8 w-8" />;
  if (name.includes('bmw')) return <Car className="h-8 w-8" />;
  if (name.includes('mercedes')) return <Car className="h-8 w-8" />;
  if (name.includes('toyota')) return <Car className="h-8 w-8" />;
  if (name.includes('ford')) return <Car className="h-8 w-8" />;

  // Gaming
  if (name.includes('nintendo')) return <Gamepad2 className="h-8 w-8" />;
  if (name.includes('playstation')) return <Gamepad2 className="h-8 w-8" />;
  if (name.includes('xbox')) return <Gamepad2 className="h-8 w-8" />;
  if (name.includes('razer')) return <Gamepad2 className="h-8 w-8" />;
  if (name.includes('logitech')) return <Gamepad2 className="h-8 w-8" />;

  // Fashion
  if (name.includes('nike')) return <Shirt className="h-8 w-8" />;
  if (name.includes('adidas')) return <Shirt className="h-8 w-8" />;
  if (name.includes('zara')) return <Shirt className="h-8 w-8" />;
  if (name.includes('h&m')) return <Shirt className="h-8 w-8" />;
  if (name.includes('uniqlo')) return <Shirt className="h-8 w-8" />;

  // Audio
  if (name.includes('bose')) return <Headphones className="h-8 w-8" />;
  if (name.includes('beats')) return <Headphones className="h-8 w-8" />;
  if (name.includes('sennheiser')) return <Headphones className="h-8 w-8" />;

  // Watches
  if (name.includes('rolex')) return <Watch className="h-8 w-8" />;
  if (name.includes('omega')) return <Watch className="h-8 w-8" />;
  if (name.includes('casio')) return <Watch className="h-8 w-8" />;

  // Default icon for unknown brands
  return <Building2 className="h-8 w-8" />;
};

const ProductBrandsGrid: React.FC = () => {
  const navigate = useNavigate();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch brands from backend on component mount
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setLoading(true);
        setError(null);

        const brandNames = await getBrands();

        // Transform backend data to component format
        const transformedBrands: Brand[] = brandNames.map((name) => ({
          id: name,
          name: name,
          icon: getBrandIcon(name)
        }));

        setBrands(transformedBrands);
      } catch (err) {
        console.error('Error fetching brands:', err);
        setError('Failed to load brands');
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, []);

  const handleBrandClick = (brandName: string) => {
    navigate(`/search?brand=${encodeURIComponent(brandName)}`);
  };

  const handleSeeMore = () => {
    navigate('/brands');
  };

  // Show loading state
  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading brands...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Don't render if no brands
  if (brands.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
            Shop by Brand
          </h2>

          <div className="flex items-center gap-2">
            {/* Navigation arrows */}
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full border-gray-300 hover:bg-gray-50"
              onClick={() => {
                const container = document.getElementById('brands-grid');
                if (container) {
                  container.scrollBy({ left: -300, behavior: 'smooth' });
                }
              }}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full border-gray-300 hover:bg-gray-50"
              onClick={() => {
                const container = document.getElementById('brands-grid');
                if (container) {
                  container.scrollBy({ left: 300, behavior: 'smooth' });
                }
              }}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* See more button */}
            <Button
              variant="outline"
              className="ml-2 text-blue-600 border-blue-600 hover:bg-blue-50"
              onClick={handleSeeMore}
            >
              See more
            </Button>
          </div>
        </div>

        {/* Brands Grid */}
        <div
          id="brands-grid"
          className="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {brands.map((brand) => (
            <div
              key={brand.id}
              onClick={() => handleBrandClick(brand.name)}
              className="flex-shrink-0 w-40 cursor-pointer group"
            >
              <div className="flex flex-col items-center text-center p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                {/* Icon */}
                <div className="mb-3 text-gray-600 group-hover:text-green-600 transition-colors duration-200">
                  {brand.icon}
                </div>

                {/* Brand Name */}
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-green-600 transition-colors duration-200">
                  {brand.name}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile See More Button */}
        <div className="mt-6 text-center md:hidden">
          <Button
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleSeeMore}
          >
            See all brands
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProductBrandsGrid;
