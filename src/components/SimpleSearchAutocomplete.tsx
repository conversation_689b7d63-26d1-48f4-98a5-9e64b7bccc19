import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { getSearchSuggestions, SearchSuggestion } from '../services/newApi';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Tag, Box, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchAutocompleteProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string) => void;
  autoFocus?: boolean;
}

const SimpleSearchAutocomplete: React.FC<SearchAutocompleteProps> = ({
  placeholder = 'Search products...',
  className,
  onSearch,
  autoFocus = false
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Handle clicks outside the component to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch suggestions when query changes
  useEffect(() => {
    if (query.trim().length < 2) {
      setSuggestions([]);
      return;
    }

    // Debounce the search to avoid too many requests
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    const fetchSuggestions = async () => {
      setLoading(true);
      try {
        console.log('Fetching suggestions for query:', query);
        const results = await getSearchSuggestions(query);
        console.log('Received suggestions:', results);
        setSuggestions(results);
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      } finally {
        setLoading(false);
      }
    };

    debounceTimerRef.current = setTimeout(fetchSuggestions, 300);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [query]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      if (onSearch) {
        onSearch(query);
      } else {
        navigate(`/search?q=${encodeURIComponent(query)}`);
      }
      setShowSuggestions(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setShowSuggestions(false);

    switch (suggestion.type) {
      case 'product':
        if (suggestion.slug) {
          navigate(`/products/${suggestion.slug}`);
        } else {
          setQuery(suggestion.text);
          if (onSearch) onSearch(suggestion.text);
          else navigate(`/search?q=${encodeURIComponent(suggestion.text)}`);
        }
        break;
      case 'brand':
        navigate(`/products?brand=${encodeURIComponent(suggestion.text)}`);
        break;
      case 'category':
        navigate(`/products?category=${encodeURIComponent(suggestion.text)}`);
        break;
      default:
        setQuery(suggestion.text);
        if (onSearch) onSearch(suggestion.text);
        else navigate(`/search?q=${encodeURIComponent(suggestion.text)}`);
    }
  };

  // Get icon for suggestion type
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'brand':
        return <Tag className="mr-2 h-4 w-4" />;
      case 'category':
        return <Box className="mr-2 h-4 w-4" />;
      default:
        return <Search className="mr-2 h-4 w-4" />;
    }
  };

  return (
    <div className={cn("relative", className)}>
      <form onSubmit={handleSubmit} className="w-full">
        <div className="relative w-full bg-white rounded-full shadow-lg border border-gray-200 overflow-hidden py-4">
          <Input
            ref={inputRef}
            type="search"
            placeholder={placeholder}
            className="border-0 bg-transparent pl-6 pr-16 py-4 text-base rounded-full focus:ring-0 focus:outline-none w-full placeholder:text-gray-500"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            autoFocus={autoFocus}
          />
          <Button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10 rounded-full bg-teal-600 hover:bg-teal-700 p-0 shadow-md"
            aria-label="Search"
          >
            <Search className="h-5 w-5 text-white" />
          </Button>
        </div>
      </form>

      {/* Suggestions dropdown */}
      {showSuggestions && query.trim().length >= 2 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-background rounded-md border shadow-md max-h-[60vh] overflow-y-auto"
        >
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span>Searching...</span>
            </div>
          ) : suggestions.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No results found
            </div>
          ) : (
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground px-2 py-1.5">
                Suggestions
              </div>
              {suggestions.map((suggestion, index) => (
                <div
                  key={`${suggestion.type}-${index}`}
                  className="flex items-center px-2 py-2.5 text-sm rounded-sm cursor-pointer hover:bg-muted"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  {getSuggestionIcon(suggestion.type)}
                  <span className="truncate">{suggestion.text}</span>
                  <span className="ml-2 shrink-0 text-xs text-muted-foreground">
                    {suggestion.type === 'product' ? 'Product' :
                      suggestion.type === 'brand' ? 'Brand' : 'Category'}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SimpleSearchAutocomplete;
