import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSearchParams, useNavigate } from "react-router-dom";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ProductCard from "./ProductCard";
import { getProducts } from "@/services/newApi";
import { Alert, AlertDescription } from "./ui/alert";
import { SearchResultsSkeleton } from "./ui/skeleton";
import { useErrorHandler } from "@/utils/errorHandler";
import { SlidersHorizontal, Search } from "lucide-react";
import Header from "./Header";

interface Product {
  _id: string;
  name: string;
  slug: string;
  category: string;
  subcategory: string;
  brand: string;
  image: string;
  score: number;
}

const NewSearchResults = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Get values from URL parameters
  const searchQuery = searchParams.get("q") || "";
  const categoryParam = searchParams.get("category") || "";
  const subcategoryParam = searchParams.get("subcategory") || "";
  const initialSortOption = searchParams.get("sort") || "-createdAt";

  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get("page") || "1", 10));
  const [sortOption, setSortOption] = useState(initialSortOption);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const { handleError } = useErrorHandler();

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching products with params:', {
          q: searchQuery || undefined,
          category: categoryParam || undefined,
          subcategory: subcategoryParam || undefined,
          sort: sortOption,
          page: currentPage,
          limit: 8
        });

        // Use search API if there's a search query, otherwise use regular products API
        let response;
        if (searchQuery && searchQuery.trim()) {
          // Use search endpoint for text queries - build query parameters
          const searchParams = new URLSearchParams();
          searchParams.set('q', searchQuery);
          if (categoryParam) searchParams.set('category', categoryParam);
          if (subcategoryParam) searchParams.set('subcategory', subcategoryParam);
          searchParams.set('sort', sortOption);
          searchParams.set('page', currentPage.toString());
          searchParams.set('limit', '8');

          // Use the same API base URL pattern as the rest of the app
          const API_BASE_URL = '/api'; // Use relative URL for production compatibility
          const searchUrl = `${API_BASE_URL}/search?${searchParams.toString()}`;

          const searchResponse = await fetch(searchUrl);

          if (!searchResponse.ok) {
            throw new Error(`Search failed: ${searchResponse.status} ${searchResponse.statusText}`);
          }

          const searchData = await searchResponse.json();
          response = {
            data: searchData.data || [],
            total: searchData.pagination?.total || 0,
            count: searchData.count || 0
          };
        } else {
          // Use regular products API for category browsing
          response = await getProducts({
            category: categoryParam || undefined,
            subcategory: subcategoryParam || undefined,
            sort: sortOption,
            page: currentPage,
            limit: 8 // Products per page
          });
        }

        console.log('Received response:', response);

        setProducts(response.data);
        setTotalResults(response.total);
        setTotalPages(Math.ceil(response.total / 8));
      } catch (err) {
        const appError = handleError(err, 'Failed to load search results');
        setError(appError.message);
        // Use empty results
        setProducts([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [searchQuery, categoryParam, subcategoryParam, sortOption, currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleSortChange = (value: string) => {
    setSortOption(value);
    // Update URL with new sort parameter
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("sort", value);
    navigate(`/search?${newSearchParams.toString()}`);
  };

  const handleProductClick = (product: Product) => {
    navigate(`/products/${product.slug}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
              Home
            </Button>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">
              {searchQuery ? 'Search Results' : categoryParam ? 'Category' : 'Products'}
            </span>
            {(searchQuery || categoryParam) && (
              <>
                <span className="mx-2">/</span>
                <span className="text-gray-900 font-medium truncate max-w-[200px]">
                  {searchQuery ? `"${searchQuery}"` : categoryParam}
                </span>
              </>
            )}
            {subcategoryParam && (
              <>
                <span className="mx-2">/</span>
                <span className="text-gray-900 font-medium truncate max-w-[200px]">
                  {subcategoryParam}
                </span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">


        {/* Search results header */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-900">
            {searchQuery ? (
              <>
                Search Results for <span className="text-primary">"{searchQuery}"</span>
                {categoryParam && <span className="text-gray-600"> in {categoryParam}</span>}
                {subcategoryParam && <span className="text-gray-600"> {' > '} {subcategoryParam}</span>}
              </>
            ) : categoryParam ? (
              <>
                {categoryParam} Products
                {subcategoryParam && <span className="text-gray-600"> {' > '} {subcategoryParam}</span>}
              </>
            ) : (
              'All Products'
            )}
          </h1>
          <p className="text-gray-600">
            {!loading && (
              totalResults > 0 ? (
                <>
                  Found <span className="font-semibold text-gray-900">{totalResults.toLocaleString()}</span>
                  {totalResults === 1 ? ' product' : ' products'}
                  {searchQuery && ` matching "${searchQuery}"`}
                  {categoryParam && !searchQuery && ` in ${categoryParam}`}
                </>
              ) : (
                searchQuery ? (
                  <>No products found for <span className="font-semibold">"{searchQuery}"</span></>
                ) : categoryParam ? (
                  <>No products found in <span className="font-semibold">{categoryParam}</span></>
                ) : (
                  'No products found'
                )
              )
            )}
          </p>
        </div>

        {/* Filters and sorting */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8 pb-4 border-b">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4" /> Filters
            </Button>

            {categoryParam && (
              <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1">
                {categoryParam}
                <button className="ml-1 text-gray-500 hover:text-gray-700" onClick={() => {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.delete("category");
                  newSearchParams.delete("subcategory"); // Also remove subcategory when removing category
                  navigate(`/search?${newSearchParams.toString()}`);
                }}>×</button>
              </Badge>
            )}

            {subcategoryParam && (
              <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 flex items-center gap-1">
                {subcategoryParam}
                <button className="ml-1 text-blue-500 hover:text-blue-700" onClick={() => {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.delete("subcategory");
                  navigate(`/search?${newSearchParams.toString()}`);
                }}>×</button>
              </Badge>
            )}
          </div>

          <div className="w-full sm:w-auto flex flex-row items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Select value={sortOption} onValueChange={handleSortChange}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="-createdAt">Newest First</SelectItem>
                <SelectItem value="createdAt">Oldest First</SelectItem>
                <SelectItem value="name">Name (A-Z)</SelectItem>
                <SelectItem value="-name">Name (Z-A)</SelectItem>
                <SelectItem value="-score">Highest Score</SelectItem>
                <SelectItem value="score">Lowest Score</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Loading state */}
        {loading && <SearchResultsSkeleton />}

        {/* Error state */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Empty state */}
        {!loading && products.length === 0 && !error && (
          <div className="text-center py-16 bg-gray-50 rounded-lg border">
            <Search className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {searchQuery ? `No products found for "${searchQuery}"` : 'No products found'}
            </h2>
            <div className="text-gray-600 mb-8 max-w-md mx-auto">
              {searchQuery ? (
                <div className="space-y-2">
                  <p>We couldn't find any products matching your search.</p>
                  <p className="text-sm">Try:</p>
                  <ul className="text-sm space-y-1">
                    <li>• Checking your spelling</li>
                    <li>• Using different keywords</li>
                    <li>• Searching for a broader term</li>
                    <li>• Browsing our categories instead</li>
                  </ul>
                </div>
              ) : (
                <p>Try adjusting your filters or browse our categories to find products.</p>
              )}
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={() => navigate("/")} variant="default">
                Return to Home
              </Button>
              <Button onClick={() => navigate("/categories")} variant="outline">
                Browse Categories
              </Button>
              {searchQuery && (
                <Button
                  onClick={() => {
                    const newSearchParams = new URLSearchParams();
                    if (categoryParam) newSearchParams.set("category", categoryParam);
                    navigate(`/search?${newSearchParams.toString()}`);
                  }}
                  variant="outline"
                >
                  Clear Search
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Product grid */}
        {!loading && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
            {products.map((product) => (
              <div key={product._id}>
                <ProductCard
                  id={product._id}
                  slug={product.slug}
                  name={product.name}
                  image={product.image}
                  rating={4.5} // Default rating since we don't have this in our new schema
                  reviewCount={0} // Default review count
                  sources={[product.brand]}
                  score={product.score}
                  category={product.category}
                  onClick={() => handleProductClick(product)}
                />
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 mb-4">
            <Pagination>
              <PaginationContent className="flex flex-wrap justify-center">
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    className={
                      currentPage === 1 ? "pointer-events-none opacity-50" : ""
                    }
                  />
                </PaginationItem>

                {/* Mobile: Show limited page numbers */}
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                  // On mobile, only show current page, first, last, and one page before and after current
                  const showOnMobile =
                    page === 1 ||
                    page === totalPages ||
                    page === currentPage ||
                    page === currentPage - 1 ||
                    page === currentPage + 1;

                  // For desktop, show all pages
                  return (
                    <PaginationItem key={page} className={showOnMobile ? "" : "hidden sm:block"}>
                      {/* Add ellipsis for skipped pages on mobile */}
                      {showOnMobile && page > 1 && page === currentPage - 1 && currentPage > 3 && (
                        <PaginationEllipsis className="hidden xs:block" />
                      )}

                      <PaginationLink
                        isActive={currentPage === page}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </PaginationLink>

                      {/* Add ellipsis for skipped pages on mobile */}
                      {showOnMobile && page < totalPages && page === currentPage + 1 && currentPage < totalPages - 2 && (
                        <PaginationEllipsis className="hidden xs:block" />
                      )}
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    className={
                      currentPage === totalPages
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>

            <div className="text-center text-sm text-gray-500 mt-2">
              Showing page {currentPage} of {totalPages}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewSearchResults;
