/**
 * New API service for communicating with the backend
 */

// Base URL for API requests - use environment variable or fallback to relative URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Log the API base URL for debugging
console.log('New API Base URL:', API_BASE_URL);

// Cache for API responses
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// Cache expiration time (2 minutes for product details, 5 minutes for other data)
const PRODUCT_CACHE_EXPIRATION = 2 * 60 * 1000;
const GENERAL_CACHE_EXPIRATION = 5 * 60 * 1000;

// Cache object
const cache: Record<string, CacheEntry<any>> = {};

// Get data from cache or fetch from API
async function getFromCacheOrFetch<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  forceRefresh = false
): Promise<T> {
  // Create a full cache key
  const fullCacheKey = `${cacheKey}`;

  // Check if data is in cache and not expired
  const cachedData = cache[fullCacheKey];
  const now = Date.now();

  // Use shorter expiration for product details
  const isProductDetail = fullCacheKey.startsWith('product:');
  const expirationTime = isProductDetail ? PRODUCT_CACHE_EXPIRATION : GENERAL_CACHE_EXPIRATION;

  if (!forceRefresh && cachedData && now - cachedData.timestamp < expirationTime) {
    console.log(`Using cached data for ${fullCacheKey}`);
    return cachedData.data;
  }

  // Fetch fresh data
  console.log(`Fetching fresh data for ${fullCacheKey}`);
  const data = await fetchFn();

  // Don't cache failed responses
  if (!data) {
    console.log(`Not caching null/undefined data for ${fullCacheKey}`);
    return data;
  }

  // Update cache
  cache[fullCacheKey] = {
    data,
    timestamp: now
  };

  return data;
}

// Types
export interface Product {
  _id: string;
  name: string;
  slug: string;
  category: string;
  subcategory: string;
  brand: string;
  // price field removed as per terms and conditions
  image: string;
  description: string;
  pros: string[];
  cons: string[];
  opinion: string;
  score: number;
  affiliateLink?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SearchParams {
  category?: string;
  subcategory?: string;
  brand?: string;
  minScore?: number;
  maxScore?: number;
  page?: number;
  limit?: number;
  sort?: string;
  select?: string;
}

export interface SearchResponse {
  success: boolean;
  count: number;
  pagination: {
    next?: { page: number; limit: number };
    prev?: { page: number; limit: number };
  };
  total: number;
  data: Product[];
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// Get auth token from local storage
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// Set auth token in local storage
const setToken = (token: string): void => {
  localStorage.setItem('token', token);
  // Store login timestamp for auto-logout after a week
  localStorage.setItem('loginTimestamp', Date.now().toString());
};

// Remove auth token and related data from local storage
const removeToken = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('loginTimestamp');
  // Clear any cached data
  clearAllCache();
};

/**
 * Clear all cached data
 */
export const clearAllCache = (): void => {
  Object.keys(cache).forEach(key => delete cache[key]);
  console.log('All cache cleared');
};

/**
 * Clear product cache
 * @param productId Optional product ID to clear specific product cache
 */
export const clearProductCache = (productId?: string): void => {
  if (productId) {
    // Clear specific product cache
    const idCacheKey = `product:id:${productId}`;
    if (cache[idCacheKey]) {
      delete cache[idCacheKey];
      console.log(`Cache cleared for ${idCacheKey}`);
    }

    // Also try to clear by slug if we have it in cache
    Object.keys(cache).forEach(key => {
      if (key.startsWith('product:') &&
        cache[key]?.data?._id === productId) {
        delete cache[key];
        console.log(`Cache cleared for ${key}`);
      }
    });
  } else {
    // Clear all product cache
    Object.keys(cache).forEach(key => {
      if (key.startsWith('product:')) {
        delete cache[key];
      }
    });
    console.log('All product cache cleared');
  }
};

/**
 * Check if token has expired (1 week = 7 * 24 * 60 * 60 * 1000 = 604800000 ms)
 * @returns Boolean indicating if token has expired
 */
const isTokenExpired = (): boolean => {
  const loginTimestamp = localStorage.getItem('loginTimestamp');
  if (!loginTimestamp) return true;

  const oneWeek = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
  const now = Date.now();
  const loginTime = parseInt(loginTimestamp, 10);

  return now - loginTime > oneWeek;
};

// Import CSRF utilities
import { fetchWithCsrf } from '../utils/csrf';

/**
 * Login user
 * @param email User email
 * @param password User password
 * @returns Promise with auth response
 */
export const login = async (email: string, password: string): Promise<AuthResponse> => {
  try {
    // First, make a GET request to get the CSRF token
    const tokenResponse = await fetch(`${API_BASE_URL}/auth/csrf-token`, {
      credentials: 'include' // Important for cookies
    });

    // Now use fetchWithCsrf for the login request
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/auth/login`,
      'POST',
      { email, password }
    );

    // Save token to local storage
    if (data.token) {
      setToken(data.token);
    }

    return data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

/**
 * Logout user
 * @param redirectUrl Optional URL to redirect to after logout
 */
export const logout = (redirectUrl?: string): void => {
  removeToken();

  // If redirect URL is provided, redirect to that URL
  if (redirectUrl) {
    window.location.href = redirectUrl;
  }
};

/**
 * Check if user is authenticated
 * @returns Boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();

  // If no token or token is expired, auto-logout
  if (!token || isTokenExpired()) {
    if (token) {
      // Only call logout if there was a token (user was logged in)
      logout();
    }
    return false;
  }

  return true;
};

/**
 * Get all products
 * @param params Search parameters
 * @returns Promise with search results
 */
export const getProducts = async (params: SearchParams = {}, forceRefresh = false): Promise<SearchResponse> => {
  // Build query string from params
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  const queryString = queryParams.toString();
  const cacheKey = `products:${queryString || 'all'}`;

  return getFromCacheOrFetch<SearchResponse>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/products${queryString ? `?${queryString}` : ''}`;
        console.log('Fetching from URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get products');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get products: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received data:', data);
        return data;
      } catch (error) {
        console.error('Error getting products:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product by slug
 * @param slug Product slug
 * @returns Promise with product
 */
export const getProductBySlug = async (slug: string, forceRefresh = false): Promise<Product> => {
  const cacheKey = `product:${slug}`;

  return getFromCacheOrFetch<Product>(
    cacheKey,
    async () => {
      try {
        console.log(`Fetching product with slug: ${slug}`);
        const url = `${API_BASE_URL}/products/${slug}`;
        console.log('Request URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get product details');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get product details: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received product data:', data);
        return data.data;
      } catch (error) {
        console.error('Error getting product details:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product by ID
 * @param id Product ID
 * @returns Promise with product
 */
export const getProductById = async (id: string, forceRefresh = false): Promise<Product> => {
  const cacheKey = `product:id:${id}`;

  return getFromCacheOrFetch<Product>(
    cacheKey,
    async () => {
      try {
        console.log(`Fetching product with ID: ${id}`);
        const url = `${API_BASE_URL}/products/id/${id}`;
        console.log('Request URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get product details');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get product details: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received product data:', data);
        return data.data;
      } catch (error) {
        console.error('Error getting product details:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Create a new product (admin only)
 * @param product Product data
 * @returns Promise with created product
 */
export const createProduct = async (product: Omit<Product, '_id' | 'slug' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/products`,
      'POST',
      product,
      { 'Authorization': `Bearer ${token}` }
    );

    return data.data;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
};

/**
 * Update a product (admin only)
 * @param id Product ID
 * @param product Product data to update
 * @returns Promise with updated product
 */
export const updateProduct = async (id: string, product: Partial<Omit<Product, '_id' | 'slug' | 'createdAt' | 'updatedAt'>>): Promise<Product> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/products/${id}`,
      'PUT',
      product,
      { 'Authorization': `Bearer ${token}` }
    );

    // Update cache
    const cacheKey = `product:id:${id}`;
    cache[cacheKey] = {
      data: data.data,
      timestamp: Date.now()
    };

    return data.data;
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
};

/**
 * Delete a product (admin only)
 * @param id Product ID
 * @returns Promise with success status
 */
export const deleteProduct = async (id: string): Promise<void> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    await fetchWithCsrf(
      `${API_BASE_URL}/products/${id}`,
      'DELETE',
      null,
      { 'Authorization': `Bearer ${token}` }
    );

    // Remove from cache
    const cacheKey = `product:id:${id}`;
    delete cache[cacheKey];

  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
};

/**
 * Category with subcategories interface
 */
export interface CategoryWithSubcategories {
  category: string;
  subcategories: string[];
}

/**
 * Get all product categories
 * @returns Promise with categories array
 */
export const getCategories = async (): Promise<string[]> => {
  const cacheKey = 'categories';

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories`;
        console.log('Fetching categories from URL:', url);

        const response = await fetch(url);
        console.log('Categories response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to fetch categories');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received categories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }
    },
    false // Don't force refresh by default
  );
};

/**
 * Get all product categories with their subcategories
 * @returns Promise with categories and subcategories array
 */
export const getCategoriesWithSubcategories = async (): Promise<CategoryWithSubcategories[]> => {
  const cacheKey = 'categories-with-subcategories';

  return getFromCacheOrFetch<CategoryWithSubcategories[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories/with-subcategories`;
        console.log('Fetching categories with subcategories from URL:', url);

        const response = await fetch(url);
        console.log('Categories with subcategories response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch categories with subcategories: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received categories with subcategories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching categories with subcategories:', error);
        throw error;
      }
    },
    false
  );
};

/**
 * Get subcategories for a specific category
 * @param category Category name
 * @returns Promise with subcategories array
 */
export const getSubcategories = async (category: string): Promise<string[]> => {
  const cacheKey = `subcategories:${category}`;

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories/${encodeURIComponent(category)}/subcategories`;
        console.log('Fetching subcategories from URL:', url);

        const response = await fetch(url);
        console.log('Subcategories response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch subcategories: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received subcategories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching subcategories:', error);
        throw error;
      }
    },
    false
  );
};



// Score is now a percentage (0-100) instead of predefined categories

/**
 * Search for products
 * @param query Search query
 * @returns Promise with search results
 */
export const searchProducts = async (query: string): Promise<Product[]> => {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    const url = `${API_BASE_URL}/search?q=${encodeURIComponent(query)}`;
    console.log('Searching products from URL:', url);

    const response = await fetch(url);
    console.log('Search response status:', response.status);

    // Check if the response is OK
    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      console.error('Content-Type:', contentType);

      // Only try to parse as JSON if the content type is JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || 'Failed to search products');
        } catch (e) {
          console.error('Failed to parse error response as JSON:', e);
        }
      }

      // If we couldn't parse as JSON or it's not JSON, throw a generic error
      throw new Error(`Failed to search products: ${response.status} ${response.statusText}`);
    }

    // Check content type before parsing
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Unexpected content type:', contentType);
      throw new Error('Server returned non-JSON response');
    }

    const data = await response.json();
    console.log('Received search data:', data);
    return data.data;
  } catch (error) {
    console.error('Error searching products:', error);
    throw error;
  }
};

/**
 * Category trending products response interface
 */
export interface CategoryTrendingResponse {
  success: boolean;
  data: {
    category: string;
    products: Product[];
  }[];
  fromCache?: boolean;
}

/**
 * Get trending products by category
 * @param category Optional category to filter trending products
 * @param timeFrame Time frame for trending calculation ('day', 'week', 'month')
 * @param forceRefresh Whether to force a refresh of the cache
 * @returns Promise with trending products by category
 */
export const getTrendingByCategory = async (
  category?: string,
  timeFrame: 'day' | 'week' | 'month' = 'week',
  forceRefresh = false
): Promise<CategoryTrendingResponse> => {
  // Build query string
  const params = new URLSearchParams();
  if (category) {
    params.append('category', category);
  }
  params.append('timeFrame', timeFrame);

  const queryString = params.toString();
  const cacheKey = `trending:${queryString || 'all'}`;

  return getFromCacheOrFetch<CategoryTrendingResponse>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/products/trending-by-category${queryString ? `?${queryString}` : ''}`;
        console.log('Fetching trending products from URL:', url);

        const response = await fetch(url);
        console.log('Trending products response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to fetch trending products');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to fetch trending products: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received trending products data:', data);
        return data;
      } catch (error) {
        console.error('Error fetching trending products by category:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get search suggestions for autocomplete
 * @param query Search query
 * @returns Promise with search suggestions
 */
export interface SearchSuggestion {
  type: 'product' | 'brand' | 'category' | 'subcategory';
  text: string;
  slug?: string;
}

export const getSearchSuggestions = async (query: string): Promise<SearchSuggestion[]> => {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    const url = `${API_BASE_URL}/search/suggestions?q=${encodeURIComponent(query)}`;
    console.log('Fetching search suggestions from URL:', url);

    const response = await fetch(url);
    console.log('Search suggestions response status:', response.status);

    // Check if the response is OK
    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      console.error('Content-Type:', contentType);

      // Only try to parse as JSON if the content type is JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || 'Failed to get search suggestions');
        } catch (e) {
          console.error('Failed to parse error response as JSON:', e);
        }
      }

      // If we couldn't parse as JSON or it's not JSON, throw a generic error
      throw new Error(`Failed to get search suggestions: ${response.status} ${response.statusText}`);
    }

    // Check content type before parsing
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Unexpected content type:', contentType);
      throw new Error('Server returned non-JSON response');
    }

    const data = await response.json();
    console.log('Received search suggestions data:', data);
    return data.data;
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    // Return empty array instead of throwing to prevent UI disruption
    return [];
  }
};
