/**
 * New API service for communicating with the backend
 */

// Base URL for API requests - use environment variable or fallback to relative URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Log the API base URL for debugging
console.log('New API Base URL:', API_BASE_URL);

// Cache for API responses
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// Cache expiration time (2 minutes for product details, 5 minutes for other data)
const PRODUCT_CACHE_EXPIRATION = 2 * 60 * 1000;
const GENERAL_CACHE_EXPIRATION = 5 * 60 * 1000;

// Cache object
const cache: Record<string, CacheEntry<any>> = {};

// Get data from cache or fetch from API
async function getFromCacheOrFetch<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  forceRefresh = false
): Promise<T> {
  // Create a full cache key
  const fullCacheKey = `${cacheKey}`;

  // Check if data is in cache and not expired
  const cachedData = cache[fullCacheKey];
  const now = Date.now();

  // Use shorter expiration for product details
  const isProductDetail = fullCacheKey.startsWith('product:');
  const expirationTime = isProductDetail ? PRODUCT_CACHE_EXPIRATION : GENERAL_CACHE_EXPIRATION;

  if (!forceRefresh && cachedData && now - cachedData.timestamp < expirationTime) {
    console.log(`Using cached data for ${fullCacheKey}`);
    return cachedData.data;
  }

  // Fetch fresh data
  console.log(`Fetching fresh data for ${fullCacheKey}`);
  const data = await fetchFn();

  // Don't cache failed responses
  if (!data) {
    console.log(`Not caching null/undefined data for ${fullCacheKey}`);
    return data;
  }

  // Update cache
  cache[fullCacheKey] = {
    data,
    timestamp: now
  };

  return data;
}

// Types
export interface Product {
  _id: string;
  name: string;
  slug: string;
  category: string;
  subcategory: string;
  brand: string;
  // price field removed as per terms and conditions
  image: string;
  description: string;
  pros: string[];
  cons: string[];
  opinion: string;
  score: number;
  affiliateLink?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SearchParams {
  category?: string;
  subcategory?: string;
  brand?: string;
  minScore?: number;
  maxScore?: number;
  page?: number;
  limit?: number;
  sort?: string;
  select?: string;
}

export interface SearchResponse {
  success: boolean;
  count: number;
  pagination: {
    next?: { page: number; limit: number };
    prev?: { page: number; limit: number };
  };
  total: number;
  data: Product[];
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// Get auth token from local storage
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// Set auth token in local storage
const setToken = (token: string): void => {
  localStorage.setItem('token', token);
  // Store login timestamp for auto-logout after a week
  localStorage.setItem('loginTimestamp', Date.now().toString());
};

// Remove auth token and related data from local storage
const removeToken = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('loginTimestamp');
  // Clear any cached data
  clearAllCache();
};

/**
 * Clear all cached data
 */
export const clearAllCache = (): void => {
  Object.keys(cache).forEach(key => delete cache[key]);
  console.log('All cache cleared');
};

/**
 * Clear product cache
 * @param productId Optional product ID to clear specific product cache
 */
export const clearProductCache = (productId?: string): void => {
  if (productId) {
    // Clear specific product cache
    const idCacheKey = `product:id:${productId}`;
    if (cache[idCacheKey]) {
      delete cache[idCacheKey];
      console.log(`Cache cleared for ${idCacheKey}`);
    }

    // Also try to clear by slug if we have it in cache
    Object.keys(cache).forEach(key => {
      if (key.startsWith('product:') &&
        cache[key]?.data?._id === productId) {
        delete cache[key];
        console.log(`Cache cleared for ${key}`);
      }
    });
  } else {
    // Clear all product cache
    Object.keys(cache).forEach(key => {
      if (key.startsWith('product:')) {
        delete cache[key];
      }
    });
    console.log('All product cache cleared');
  }
};

/**
 * Check if token has expired (1 week = 7 * 24 * 60 * 60 * 1000 = 604800000 ms)
 * @returns Boolean indicating if token has expired
 */
const isTokenExpired = (): boolean => {
  const loginTimestamp = localStorage.getItem('loginTimestamp');
  if (!loginTimestamp) return true;

  const oneWeek = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
  const now = Date.now();
  const loginTime = parseInt(loginTimestamp, 10);

  return now - loginTime > oneWeek;
};

// Import CSRF utilities
import { fetchWithCsrf } from '../utils/csrf';

/**
 * Login user
 * @param email User email
 * @param password User password
 * @returns Promise with auth response
 */
export const login = async (email: string, password: string): Promise<AuthResponse> => {
  try {
    // First, make a GET request to get the CSRF token
    const tokenResponse = await fetch(`${API_BASE_URL}/auth/csrf-token`, {
      credentials: 'include' // Important for cookies
    });

    // Now use fetchWithCsrf for the login request
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/auth/login`,
      'POST',
      { email, password }
    );

    // Save token to local storage
    if (data.token) {
      setToken(data.token);
    }

    return data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

/**
 * Logout user
 * @param redirectUrl Optional URL to redirect to after logout
 */
export const logout = (redirectUrl?: string): void => {
  removeToken();

  // If redirect URL is provided, redirect to that URL
  if (redirectUrl) {
    window.location.href = redirectUrl;
  }
};

/**
 * Check if user is authenticated
 * @returns Boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();

  // If no token or token is expired, auto-logout
  if (!token || isTokenExpired()) {
    if (token) {
      // Only call logout if there was a token (user was logged in)
      logout();
    }
    return false;
  }

  return true;
};

/**
 * Get all products
 * @param params Search parameters
 * @returns Promise with search results
 */
export const getProducts = async (params: SearchParams = {}, forceRefresh = false): Promise<SearchResponse> => {
  // Build query string from params
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  const queryString = queryParams.toString();
  const cacheKey = `products:${queryString || 'all'}`;

  return getFromCacheOrFetch<SearchResponse>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/products${queryString ? `?${queryString}` : ''}`;
        console.log('Fetching from URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get products');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get products: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received data:', data);
        return data;
      } catch (error) {
        console.error('Error getting products:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product by slug
 * @param slug Product slug
 * @returns Promise with product
 */
export const getProductBySlug = async (slug: string, forceRefresh = false): Promise<Product> => {
  const cacheKey = `product:${slug}`;

  return getFromCacheOrFetch<Product>(
    cacheKey,
    async () => {
      try {
        console.log(`Fetching product with slug: ${slug}`);
        const url = `${API_BASE_URL}/products/${slug}`;
        console.log('Request URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get product details');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get product details: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received product data:', data);
        return data.data;
      } catch (error) {
        console.error('Error getting product details:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product by ID
 * @param id Product ID
 * @returns Promise with product
 */
export const getProductById = async (id: string, forceRefresh = false): Promise<Product> => {
  const cacheKey = `product:id:${id}`;

  return getFromCacheOrFetch<Product>(
    cacheKey,
    async () => {
      try {
        console.log(`Fetching product with ID: ${id}`);
        const url = `${API_BASE_URL}/products/id/${id}`;
        console.log('Request URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to get product details');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to get product details: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received product data:', data);
        return data.data;
      } catch (error) {
        console.error('Error getting product details:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Create a new product (admin only)
 * @param product Product data
 * @returns Promise with created product
 */
export const createProduct = async (product: Omit<Product, '_id' | 'slug' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/products`,
      'POST',
      product,
      { 'Authorization': `Bearer ${token}` }
    );

    return data.data;
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
};

/**
 * Update a product (admin only)
 * @param id Product ID
 * @param product Product data to update
 * @returns Promise with updated product
 */
export const updateProduct = async (id: string, product: Partial<Omit<Product, '_id' | 'slug' | 'createdAt' | 'updatedAt'>>): Promise<Product> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    const data = await fetchWithCsrf(
      `${API_BASE_URL}/products/${id}`,
      'PUT',
      product,
      { 'Authorization': `Bearer ${token}` }
    );

    // Update cache
    const cacheKey = `product:id:${id}`;
    cache[cacheKey] = {
      data: data.data,
      timestamp: Date.now()
    };

    return data.data;
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
};

/**
 * Delete a product (admin only)
 * @param id Product ID
 * @returns Promise with success status
 */
export const deleteProduct = async (id: string): Promise<void> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    // Use fetchWithCsrf for CSRF protection
    await fetchWithCsrf(
      `${API_BASE_URL}/products/${id}`,
      'DELETE',
      null,
      { 'Authorization': `Bearer ${token}` }
    );

    // Remove from cache
    const cacheKey = `product:id:${id}`;
    delete cache[cacheKey];

  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
};

/**
 * Category with subcategories interface
 */
export interface CategoryWithSubcategories {
  category: string;
  subcategories: string[];
}

/**
 * Get all product categories
 * @returns Promise with categories array
 */
export const getCategories = async (): Promise<string[]> => {
  const cacheKey = 'categories';

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories`;
        console.log('Fetching categories from URL:', url);

        const response = await fetch(url);
        console.log('Categories response status:', response.status);

        // Check if the response is OK
        if (!response.ok) {
          const contentType = response.headers.get('content-type');
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          console.error('Content-Type:', contentType);

          // Only try to parse as JSON if the content type is JSON
          if (contentType && contentType.includes('application/json')) {
            try {
              const errorData = JSON.parse(errorText);
              throw new Error(errorData.message || 'Failed to fetch categories');
            } catch (e) {
              console.error('Failed to parse error response as JSON:', e);
            }
          }

          // If we couldn't parse as JSON or it's not JSON, throw a generic error
          throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
        }

        // Check content type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Unexpected content type:', contentType);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received categories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }
    },
    false // Don't force refresh by default
  );
};

/**
 * Get all product categories with their subcategories
 * @returns Promise with categories and subcategories array
 */
export const getCategoriesWithSubcategories = async (): Promise<CategoryWithSubcategories[]> => {
  const cacheKey = 'categories-with-subcategories';

  return getFromCacheOrFetch<CategoryWithSubcategories[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories/with-subcategories`;
        console.log('Fetching categories with subcategories from URL:', url);

        const response = await fetch(url);
        console.log('Categories with subcategories response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch categories with subcategories: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received categories with subcategories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching categories with subcategories:', error);
        throw error;
      }
    },
    false
  );
};

/**
 * Get subcategories for a specific category
 * @param category Category name
 * @returns Promise with subcategories array
 */
export const getSubcategories = async (category: string): Promise<string[]> => {
  const cacheKey = `subcategories:${category}`;

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/categories/${encodeURIComponent(category)}/subcategories`;
        console.log('Fetching subcategories from URL:', url);

        const response = await fetch(url);
        console.log('Subcategories response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch subcategories: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received subcategories data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching subcategories:', error);
        throw error;
      }
    },
    false
  );
};

// ===== BRAND API FUNCTIONS =====

/**
 * Get all brands
 * @returns Promise with brands array
 */
export const getBrands = async (): Promise<string[]> => {
  const cacheKey = 'brands';

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/brands`;
        console.log('Fetching brands from URL:', url);

        const response = await fetch(url);
        console.log('Brands response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch brands: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received brands data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching brands:', error);
        throw error;
      }
    },
    true // Cache brands for longer since they change less frequently
  );
};

/**
 * Brand data interface
 */
export interface BrandData {
  name: string;
  productCount: number;
  averageScore: number;
  topProducts: Array<{
    id: string;
    name: string;
    image: string;
    score: number;
    slug: string;
  }>;
}

/**
 * Get brands with statistics
 * @returns Promise with brands and their stats
 */
export const getBrandsWithStats = async (): Promise<BrandData[]> => {
  const cacheKey = 'brands-with-stats';

  return getFromCacheOrFetch<BrandData[]>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/brands/with-stats`;
        console.log('Fetching brands with stats from URL:', url);

        const response = await fetch(url);
        console.log('Brands with stats response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch brands with stats: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received brands with stats data:', data);
        return data.data;
      } catch (error) {
        console.error('Error fetching brands with stats:', error);
        throw error;
      }
    },
    false
  );
};

/**
 * Get products for a specific brand
 * @param brand Brand name
 * @param options Query options
 * @returns Promise with products response
 */
export const getBrandProducts = async (
  brand: string,
  options: {
    page?: number;
    limit?: number;
    sort?: string;
  } = {}
): Promise<ProductsResponse> => {
  const { page = 1, limit = 10, sort = 'brandScore' } = options;
  const cacheKey = `brand-products:${brand}:${page}:${limit}:${sort}`;

  return getFromCacheOrFetch<ProductsResponse>(
    cacheKey,
    async () => {
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          sort
        });

        const url = `${API_BASE_URL}/brands/${encodeURIComponent(brand)}/products?${params}`;
        console.log('Fetching brand products from URL:', url);

        const response = await fetch(url);
        console.log('Brand products response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to fetch brand products: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received brand products data:', data);
        return data;
      } catch (error) {
        console.error('Error fetching brand products:', error);
        throw error;
      }
    },
    false
  );
};

/**
 * Search brands by name
 * @param query Search query
 * @param limit Maximum number of results
 * @returns Promise with matching brands
 */
export const searchBrands = async (query: string, limit: number = 10): Promise<string[]> => {
  if (!query.trim()) {
    return [];
  }

  const cacheKey = `brand-search:${query}:${limit}`;

  return getFromCacheOrFetch<string[]>(
    cacheKey,
    async () => {
      try {
        const params = new URLSearchParams({
          q: query,
          limit: limit.toString()
        });

        const url = `${API_BASE_URL}/brands/search?${params}`;
        console.log('Searching brands from URL:', url);

        const response = await fetch(url);
        console.log('Brand search response status:', response.status);

        if (!response.ok) {
          throw new Error(`Failed to search brands: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log('Received brand search data:', data);
        return data.data;
      } catch (error) {
        console.error('Error searching brands:', error);
        throw error;
      }
    },
    false,
    300 // Cache search results for 5 minutes
  );
};

// ===== ADMIN BRAND MANAGEMENT API FUNCTIONS =====

/**
 * Admin Brand interface
 */
export interface AdminBrand {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  category: string;
  isActive: boolean;
  productCount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Get all brands for admin
 * @param options Query options
 * @returns Promise with brands response
 */
export const getAdminBrands = async (options: {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isActive?: boolean;
} = {}): Promise<{
  success: boolean;
  count: number;
  pagination: any;
  data: AdminBrand[];
}> => {
  try {
    const params = new URLSearchParams();
    if (options.page) params.set('page', options.page.toString());
    if (options.limit) params.set('limit', options.limit.toString());
    if (options.search) params.set('search', options.search);
    if (options.category && options.category !== 'all') params.set('category', options.category);
    if (options.isActive !== undefined) params.set('isActive', options.isActive.toString());

    const url = `${API_BASE_URL}/admin/brands?${params}`;
    console.log('Fetching admin brands from URL:', url);

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch admin brands: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Received admin brands data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching admin brands:', error);
    throw error;
  }
};

/**
 * Create a new brand
 * @param brandData Brand data
 * @returns Promise with created brand
 */
export const createAdminBrand = async (brandData: {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  category?: string;
  isActive?: boolean;
}): Promise<AdminBrand> => {
  try {
    const url = `${API_BASE_URL}/admin/brands`;
    console.log('Creating brand:', brandData);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(brandData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to create brand: ${response.status}`);
    }

    const data = await response.json();
    console.log('Created brand:', data);
    return data.data;
  } catch (error) {
    console.error('Error creating brand:', error);
    throw error;
  }
};

/**
 * Update a brand
 * @param id Brand ID
 * @param brandData Updated brand data
 * @returns Promise with updated brand
 */
export const updateAdminBrand = async (id: string, brandData: {
  name?: string;
  description?: string;
  logo?: string;
  website?: string;
  category?: string;
  isActive?: boolean;
}): Promise<AdminBrand> => {
  try {
    const url = `${API_BASE_URL}/admin/brands/${id}`;
    console.log('Updating brand:', id, brandData);

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(brandData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update brand: ${response.status}`);
    }

    const data = await response.json();
    console.log('Updated brand:', data);
    return data.data;
  } catch (error) {
    console.error('Error updating brand:', error);
    throw error;
  }
};

/**
 * Delete a brand
 * @param id Brand ID
 * @returns Promise
 */
export const deleteAdminBrand = async (id: string): Promise<void> => {
  try {
    const url = `${API_BASE_URL}/admin/brands/${id}`;
    console.log('Deleting brand:', id);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to delete brand: ${response.status}`);
    }

    console.log('Deleted brand:', id);
  } catch (error) {
    console.error('Error deleting brand:', error);
    throw error;
  }
};

/**
 * Toggle brand active status
 * @param id Brand ID
 * @returns Promise with updated brand
 */
export const toggleAdminBrandStatus = async (id: string): Promise<AdminBrand> => {
  try {
    const url = `${API_BASE_URL}/admin/brands/${id}/toggle`;
    console.log('Toggling brand status:', id);

    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to toggle brand status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Toggled brand status:', data);
    return data.data;
  } catch (error) {
    console.error('Error toggling brand status:', error);
    throw error;
  }
};



// Score is now a percentage (0-100) instead of predefined categories

/**
 * Search for products
 * @param query Search query
 * @returns Promise with search results
 */
export const searchProducts = async (query: string): Promise<Product[]> => {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    const url = `${API_BASE_URL}/search?q=${encodeURIComponent(query)}`;
    console.log('Searching products from URL:', url);

    const response = await fetch(url);
    console.log('Search response status:', response.status);

    // Check if the response is OK
    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      console.error('Content-Type:', contentType);

      // Only try to parse as JSON if the content type is JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || 'Failed to search products');
        } catch (e) {
          console.error('Failed to parse error response as JSON:', e);
        }
      }

      // If we couldn't parse as JSON or it's not JSON, throw a generic error
      throw new Error(`Failed to search products: ${response.status} ${response.statusText}`);
    }

    // Check content type before parsing
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Unexpected content type:', contentType);
      throw new Error('Server returned non-JSON response');
    }

    const data = await response.json();
    console.log('Received search data:', data);
    return data.data;
  } catch (error) {
    console.error('Error searching products:', error);
    throw error;
  }
};






/**
 * Get search suggestions for autocomplete
 * @param query Search query
 * @returns Promise with search suggestions
 */
export interface SearchSuggestion {
  type: 'product' | 'brand' | 'category' | 'subcategory';
  text: string;
  slug?: string;
}

export const getSearchSuggestions = async (query: string): Promise<SearchSuggestion[]> => {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    const url = `${API_BASE_URL}/search/suggestions?q=${encodeURIComponent(query)}`;
    console.log('Fetching search suggestions from URL:', url);

    const response = await fetch(url);
    console.log('Search suggestions response status:', response.status);

    // Check if the response is OK
    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      console.error('Content-Type:', contentType);

      // Only try to parse as JSON if the content type is JSON
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || 'Failed to get search suggestions');
        } catch (e) {
          console.error('Failed to parse error response as JSON:', e);
        }
      }

      // If we couldn't parse as JSON or it's not JSON, throw a generic error
      throw new Error(`Failed to get search suggestions: ${response.status} ${response.statusText}`);
    }

    // Check content type before parsing
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Unexpected content type:', contentType);
      throw new Error('Server returned non-JSON response');
    }

    const data = await response.json();
    console.log('Received search suggestions data:', data);
    return data.data;
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    // Return empty array instead of throwing to prevent UI disruption
    return [];
  }
};
