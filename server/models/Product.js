const mongoose = require('mongoose');
const slugify = require('slugify');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a product name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    unique: true,
    index: true
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: [
      'Beauty & Skincare',
      'Home & Kitchen Gadgets',
      'Electronics',
      'Fashion & Apparel',
      'Watches, Jewelry & Footwear'
    ]
  },
  subcategory: {
    type: String,
    required: [true, 'Please add a subcategory']
  },
  brand: {
    type: String,
    required: [true, 'Please add a brand'],
    trim: true
  },
  // Price field removed as per terms and conditions
  image: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  pros: {
    type: [String],
    default: []
  },
  cons: {
    type: [String],
    default: []
  },
  opinion: {
    type: String,
    required: [true, 'Please add an opinion'],
    maxlength: [2000, 'Opinion cannot be more than 2000 characters']
  },
  score: {
    type: Number,
    required: [true, 'Please add a score percentage'],
    min: [0, 'Score cannot be less than 0'],
    max: [100, 'Score cannot be more than 100']
  },
  affiliateLink: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create slug from name before saving
ProductSchema.pre('save', function(next) {
  // Only update slug if name is modified or it's a new document
  if (this.isModified('name') || this.isNew) {
    this.slug = slugify(this.name, { lower: true });
  }

  // Update the updatedAt field
  this.updatedAt = Date.now();

  next();
});

// Pre-validation hook to ensure category and subcategory are compatible
ProductSchema.pre('validate', function(next) {
  if (this.category && this.subcategory) {
    const { PRODUCT_SUBCATEGORIES } = require('../utils/constants');
    const validSubcategories = PRODUCT_SUBCATEGORIES[this.category] || [];

    if (!validSubcategories.includes(this.subcategory)) {
      console.log(`Validation Error: Subcategory "${this.subcategory}" is not valid for category "${this.category}"`);
      console.log('Valid subcategories for', this.category, ':', validSubcategories);
      this.invalidate('subcategory', `Subcategory "${this.subcategory}" is not valid for category "${this.category}". Valid subcategories are: ${validSubcategories.join(', ')}`);
    }
  }
  next();
});

// Create text index for search
// Note: This will be replaced by the createIndexes.js script with weighted text index
ProductSchema.index({ name: 'text', description: 'text', brand: 'text', category: 'text', subcategory: 'text' });

// Create index for name field to support autocomplete
ProductSchema.index({ name: 1 });

// Create index for brand field
ProductSchema.index({ brand: 1 });

// Create index for category field
ProductSchema.index({ category: 1 });

// Create index for subcategory field
ProductSchema.index({ subcategory: 1 });

// Create index for score field (for sorting)
ProductSchema.index({ score: -1 });

// Create index for createdAt field (for sorting by newest)
ProductSchema.index({ createdAt: -1 });

// Create index for updatedAt field (for sorting by recently updated)
ProductSchema.index({ updatedAt: -1 });



module.exports = mongoose.model('Product', ProductSchema);
