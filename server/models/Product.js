const mongoose = require('mongoose');
const slugify = require('slugify');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a product name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    unique: true,
    index: true
  },
  // Category and subcategory are now optional for backward compatibility
  category: {
    type: String,
    trim: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  // Brand is now the primary organization method
  brand: {
    type: String,
    required: [true, 'Please add a brand'],
    trim: true,
    index: true // Enhanced indexing for brand-based queries
  },
  // Price field removed as per terms and conditions
  image: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  pros: {
    type: [String],
    default: []
  },
  cons: {
    type: [String],
    default: []
  },
  opinion: {
    type: String,
    required: [true, 'Please add an opinion'],
    maxlength: [2000, 'Opinion cannot be more than 2000 characters']
  },
  score: {
    type: Number,
    required: [true, 'Please add a score percentage'],
    min: [0, 'Score cannot be less than 0'],
    max: [100, 'Score cannot be more than 100']
  },
  affiliateLink: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create slug from name before saving
ProductSchema.pre('save', function(next) {
  // Only update slug if name is modified or it's a new document
  if (this.isModified('name') || this.isNew) {
    this.slug = slugify(this.name, { lower: true });
  }

  // Update the updatedAt field
  this.updatedAt = Date.now();

  next();
});

// Brand validation hook - ensure brand is properly formatted
ProductSchema.pre('validate', function(next) {
  if (this.brand) {
    // Trim and format brand name
    this.brand = this.brand.trim();

    // Ensure brand is not empty after trimming
    if (!this.brand) {
      this.invalidate('brand', 'Brand cannot be empty');
    }
  }
  next();
});

// Create text index for search - prioritizing brand and name
// Note: This will be replaced by the createIndexes.js script with weighted text index
ProductSchema.index({
  name: 'text',
  brand: 'text',
  description: 'text',
  category: 'text',
  subcategory: 'text'
}, {
  weights: {
    name: 10,
    brand: 8,
    description: 5,
    category: 3,
    subcategory: 2
  }
});

// Create index for name field to support autocomplete
ProductSchema.index({ name: 1 });

// Enhanced brand indexing for primary organization
ProductSchema.index({ brand: 1 });
ProductSchema.index({ brand: 1, score: -1 }); // Brand + score for sorting
ProductSchema.index({ brand: 1, createdAt: -1 }); // Brand + date for sorting

// Keep category indexes for backward compatibility and migration
ProductSchema.index({ category: 1 });
ProductSchema.index({ subcategory: 1 });

// Create index for score field (for sorting)
ProductSchema.index({ score: -1 });

// Create index for createdAt field (for sorting by newest)
ProductSchema.index({ createdAt: -1 });

// Compound index for brand-based queries with filters
ProductSchema.index({ brand: 1, score: -1, createdAt: -1 });

// Create index for updatedAt field (for sorting by recently updated)
ProductSchema.index({ updatedAt: -1 });



module.exports = mongoose.model('Product', ProductSchema);
