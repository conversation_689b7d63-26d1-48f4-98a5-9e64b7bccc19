const mongoose = require('mongoose');
const slugify = require('slugify');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a product name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  slug: {
    type: String,
    unique: true,
    index: true
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: [
      'Beauty & Skincare',
      'Home & Kitchen Gadgets',
      'Electronics',
      'Fashion & Apparel',
      'Watches, Jewelry & Footwear'
    ]
  },
  subcategory: {
    type: String,
    required: [true, 'Please add a subcategory'],
    validate: {
      validator: function(value) {
        const { PRODUCT_SUBCATEGORIES } = require('../utils/constants');
        const validSubcategories = PRODUCT_SUBCATEGORIES[this.category] || [];
        return validSubcategories.includes(value);
      },
      message: 'Subcategory must be valid for the selected category'
    }
  },
  brand: {
    type: String,
    required: [true, 'Please add a brand'],
    trim: true
  },
  // Price field removed as per terms and conditions
  image: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  pros: {
    type: [String],
    default: []
  },
  cons: {
    type: [String],
    default: []
  },
  opinion: {
    type: String,
    required: [true, 'Please add an opinion'],
    maxlength: [2000, 'Opinion cannot be more than 2000 characters']
  },
  score: {
    type: Number,
    required: [true, 'Please add a score percentage'],
    min: [0, 'Score cannot be less than 0'],
    max: [100, 'Score cannot be more than 100']
  },
  affiliateLink: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create slug from name before saving
ProductSchema.pre('save', function(next) {
  // Only update slug if name is modified or it's a new document
  if (this.isModified('name') || this.isNew) {
    this.slug = slugify(this.name, { lower: true });
  }

  // Update the updatedAt field
  this.updatedAt = Date.now();

  next();
});

// Create text index for search
// Note: This will be replaced by the createIndexes.js script with weighted text index
ProductSchema.index({ name: 'text', description: 'text', brand: 'text', category: 'text', subcategory: 'text' });

// Create index for name field to support autocomplete
ProductSchema.index({ name: 1 });

// Create index for brand field
ProductSchema.index({ brand: 1 });

// Create index for category field
ProductSchema.index({ category: 1 });

// Create index for subcategory field
ProductSchema.index({ subcategory: 1 });

// Create index for score field (for sorting)
ProductSchema.index({ score: -1 });

// Create index for createdAt field (for sorting by newest)
ProductSchema.index({ createdAt: -1 });

// Create index for updatedAt field (for sorting by recently updated)
ProductSchema.index({ updatedAt: -1 });

/**
 * Get trending products by category
 * @param {string} category - Specific category to get trending products for (optional)
 * @param {string} timeFrame - Time frame for trending calculation ('day', 'week', 'month')
 * @param {number} limit - Number of products to return per category
 * @returns {Promise<Array>} - Array of trending products by category
 */
ProductSchema.statics.getTrendingByCategory = async function(category = null, timeFrame = 'week', limit = 4) {
  // Set timeout options
  const options = { maxTimeMS: 15000 }; // 15 second timeout

  try {
    // For a specific category
    if (category && category !== 'all') {
      return this.find({ category })
        .sort({ score: -1, updatedAt: -1 })
        .limit(limit)
        .maxTimeMS(15000); // Add timeout
    }

    // For getting trending products across multiple categories
    // This aggregation returns top products from each category
    const pipeline = [
      // Group by category and get top products in each
      { $sort: { score: -1, updatedAt: -1 } },
      { $group: {
          _id: "$category",
          products: { $push: "$$ROOT" },
          categoryName: { $first: "$category" }
      }},
      // Get only the top products from each category
      { $project: {
          _id: 0,
          category: "$categoryName",
          products: { $slice: ["$products", limit] }
      }},
      // Sort categories by popularity (using the score of the top product in each category)
      { $sort: { "products.0.score": -1 } },
      // Limit to top categories
      { $limit: 5 }
    ];

    // Use a simpler query if we're having connection issues
    if (mongoose.connection.readyState !== 1) {
      console.warn('MongoDB connection not fully ready. Using simplified trending query.');
      return this.find({})
        .sort({ score: -1, updatedAt: -1 })
        .limit(20)
        .maxTimeMS(15000);
    }

    // Run the aggregation with a timeout
    return this.aggregate(pipeline, options);
  } catch (error) {
    console.error('Error in getTrendingByCategory:', error);

    // Fallback to a simpler query if aggregation fails
    console.warn('Aggregation failed. Falling back to simple query.');
    return this.find({})
      .sort({ score: -1 })
      .limit(20)
      .maxTimeMS(10000);
  }
};

module.exports = mongoose.model('Product', ProductSchema);
