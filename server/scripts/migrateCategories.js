/**
 * Migration script to update old category names to new category structure
 */

const mongoose = require('mongoose');

// Load environment variables
require('dotenv').config({ path: './.env' });

const migrateCategories = async () => {
  try {
    // Connect to MongoDB
    console.log('Connecting to:', process.env.MONGODB_URI);
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;
    const productsCollection = db.collection('products');

    // Mapping of old category names to new category names
    const categoryMigrations = {
      'Home & Kitchen': 'Home & Kitchen Gadgets',
      'Beauty & Personal Care': 'Beauty & Skincare',
      'Clothing & Fashion': 'Fashion & Apparel',
      'Jewelry & Watches': 'Watches, Jewelry & Footwear',
      'Shoes': 'Watches, Jewelry & Footwear',
      'Bags & Accessories': 'Fashion & Apparel',
      'Men\'s Clothing': 'Fashion & Apparel',
      'Women\'s Clothing': 'Fashion & Apparel',
      'Kids & Baby Clothing': 'Fashion & Apparel',
      'Makeup': 'Beauty & Skincare',
      'Skincare': 'Beauty & Skincare',
      'Hair Care': 'Beauty & Skincare',
      'Fragrances': 'Beauty & Skincare',
      'Computers & Accessories': 'Electronics',
      'Smartphones & Accessories': 'Electronics',
      'Audio & Headphones': 'Electronics',
      'Cameras & Photography': 'Electronics',
      'Wearable Technology': 'Electronics'
    };

    // Default subcategory mapping for categories that don't have subcategories yet
    const defaultSubcategories = {
      'Beauty & Skincare': 'Facial Cleansers',
      'Home & Kitchen Gadgets': 'Small Kitchen Appliances (blenders, air fryers)',
      'Electronics': 'Headphones & Earbuds',
      'Fashion & Apparel': 'Men\'s Clothing',
      'Watches, Jewelry & Footwear': 'Men\'s Watches'
    };

    console.log('\n=== Starting Category Migration ===');

    // Get all products
    const products = await productsCollection.find({}).toArray();
    console.log(`Found ${products.length} products to check`);

    let updatedCount = 0;
    let addedSubcategoryCount = 0;

    for (const product of products) {
      const updates = {};
      let needsUpdate = false;

      // Check if category needs migration
      if (categoryMigrations[product.category]) {
        updates.category = categoryMigrations[product.category];
        needsUpdate = true;
        console.log(`Migrating category: "${product.category}" → "${updates.category}" for product: ${product.name}`);
      }

      // Check if subcategory is missing
      if (!product.subcategory) {
        const categoryForSubcategory = updates.category || product.category;
        if (defaultSubcategories[categoryForSubcategory]) {
          updates.subcategory = defaultSubcategories[categoryForSubcategory];
          needsUpdate = true;
          addedSubcategoryCount++;
          console.log(`Adding default subcategory: "${updates.subcategory}" for product: ${product.name}`);
        }
      }

      // Update the product if needed
      if (needsUpdate) {
        await productsCollection.updateOne(
          { _id: product._id },
          { $set: updates }
        );
        updatedCount++;
      }
    }

    console.log('\n=== Migration Complete ===');
    console.log(`Total products checked: ${products.length}`);
    console.log(`Products updated: ${updatedCount}`);
    console.log(`Products with subcategories added: ${addedSubcategoryCount}`);

    // Verify the migration
    console.log('\n=== Verification ===');
    const updatedProducts = await productsCollection.find({}).toArray();
    
    const newCategories = ['Beauty & Skincare', 'Home & Kitchen Gadgets', 'Electronics', 'Fashion & Apparel', 'Watches, Jewelry & Footwear'];
    const categoryCounts = {};
    let missingSubcategories = 0;

    updatedProducts.forEach(product => {
      const category = product.category;
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      
      if (!product.subcategory) {
        missingSubcategories++;
        console.log(`⚠️  Product missing subcategory: ${product.name} (${product.category})`);
      }
    });

    console.log('\nCategory distribution:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      const isValid = newCategories.includes(category);
      console.log(`  ${isValid ? '✓' : '✗'} ${category}: ${count} products`);
    });

    console.log(`\nProducts missing subcategories: ${missingSubcategories}`);

  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

// Run the migration
migrateCategories();
