const Product = require('../models/Product');
const Brand = require('../models/Brand');

/**
 * @desc    Get all brands
 * @route   GET /api/brands
 * @access  Public
 */
exports.getBrands = async (req, res) => {
  try {
    // Get active brands from Brand model, fallback to products if no brands in Brand model
    const managedBrands = await Brand.find({ isActive: true }).sort({ name: 1 });

    if (managedBrands.length > 0) {
      // Use managed brands
      const brandNames = managedBrands.map(brand => brand.name);

      res.status(200).json({
        success: true,
        count: brandNames.length,
        data: brandNames
      });
    } else {
      // Fallback to distinct brands from products
      const brands = await Product.distinct('brand');

      // Filter out empty/null brands and sort alphabetically
      const validBrands = brands
        .filter(brand => brand && brand.trim())
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

      res.status(200).json({
        success: true,
        count: validBrands.length,
        data: validBrands
      });
    }
  } catch (err) {
    console.error('Error fetching brands:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get brands with product counts and statistics
 * @route   GET /api/brands/with-stats
 * @access  Public
 */
exports.getBrandsWithStats = async (req, res) => {
  try {
    // Aggregate brands with product counts and average scores
    const brandStats = await Product.aggregate([
      {
        $match: {
          brand: { $exists: true, $ne: null, $ne: '' }
        }
      },
      {
        $group: {
          _id: '$brand',
          productCount: { $sum: 1 },
          averageScore: { $avg: '$score' },
          topProducts: {
            $push: {
              id: '$_id',
              name: '$name',
              image: '$image',
              score: '$score',
              slug: '$slug'
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          name: '$_id',
          productCount: 1,
          averageScore: { $round: ['$averageScore', 1] },
          topProducts: { $slice: ['$topProducts', 3] } // Get top 3 products
        }
      },
      {
        $sort: { productCount: -1, name: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      count: brandStats.length,
      data: brandStats
    });
  } catch (err) {
    console.error('Error fetching brand stats:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get products for a specific brand
 * @route   GET /api/brands/:brand/products
 * @access  Public
 */
exports.getBrandProducts = async (req, res) => {
  try {
    const { brand } = req.params;
    const { page = 1, limit = 10, sort = '-score' } = req.query;

    // Validate brand parameter
    if (!brand || !brand.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Brand parameter is required'
      });
    }

    // Build query
    const query = {
      brand: { $regex: new RegExp(`^${brand}$`, 'i') } // Case-insensitive exact match
    };

    // Pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const startIndex = (pageNum - 1) * limitNum;

    // Get total count
    const total = await Product.countDocuments(query);
    const totalPages = Math.ceil(total / limitNum);

    // Execute query with pagination and sorting
    const products = await Product.find(query)
      .sort(sort)
      .skip(startIndex)
      .limit(limitNum);

    // Pagination info
    const pagination = {
      page: pageNum,
      limit: limitNum,
      totalPages,
      total,
      hasNextPage: pageNum < totalPages,
      hasPrevPage: pageNum > 1
    };

    if (pagination.hasNextPage) {
      pagination.next = { page: pageNum + 1, limit: limitNum };
    }

    if (pagination.hasPrevPage) {
      pagination.prev = { page: pageNum - 1, limit: limitNum };
    }

    res.status(200).json({
      success: true,
      count: products.length,
      pagination,
      data: products
    });
  } catch (err) {
    console.error('Error fetching brand products:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get brand statistics
 * @route   GET /api/brands/:brand/stats
 * @access  Public
 */
exports.getBrandStats = async (req, res) => {
  try {
    const { brand } = req.params;

    // Validate brand parameter
    if (!brand || !brand.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Brand parameter is required'
      });
    }

    // Get brand statistics
    const stats = await Product.aggregate([
      {
        $match: {
          brand: { $regex: new RegExp(`^${brand}$`, 'i') }
        }
      },
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          averageScore: { $avg: '$score' },
          highestScore: { $max: '$score' },
          lowestScore: { $min: '$score' },
          topProduct: {
            $first: {
              $cond: {
                if: { $eq: ['$score', { $max: '$score' }] },
                then: {
                  id: '$_id',
                  name: '$name',
                  score: '$score',
                  slug: '$slug'
                },
                else: null
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalProducts: 1,
          averageScore: { $round: ['$averageScore', 1] },
          highestScore: 1,
          lowestScore: 1,
          topProduct: 1
        }
      }
    ]);

    if (stats.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        brand: brand,
        ...stats[0]
      }
    });
  } catch (err) {
    console.error('Error fetching brand stats:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Search brands by name
 * @route   GET /api/brands/search
 * @access  Public
 */
exports.searchBrands = async (req, res) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || !q.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Search for brands that match the query
    const brands = await Product.distinct('brand', {
      brand: { $regex: new RegExp(q, 'i') }
    });

    // Filter and sort results
    const filteredBrands = brands
      .filter(brand => brand && brand.trim())
      .sort((a, b) => {
        // Prioritize exact matches and prefix matches
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();
        const queryLower = q.toLowerCase();

        if (aLower === queryLower) return -1;
        if (bLower === queryLower) return 1;
        if (aLower.startsWith(queryLower)) return -1;
        if (bLower.startsWith(queryLower)) return 1;
        return aLower.localeCompare(bLower);
      })
      .slice(0, parseInt(limit));

    res.status(200).json({
      success: true,
      count: filteredBrands.length,
      data: filteredBrands
    });
  } catch (err) {
    console.error('Error searching brands:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
