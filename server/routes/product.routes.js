const express = require('express');
const { body } = require('express-validator');
const {
  getProducts,
  getProductBySlug,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct
} = require('../controllers/product.controller');
const { protect, authorize } = require('../middleware/auth');
const { cacheMiddleware, setCacheHeaders, clearCache } = require('../utils/cache');

const router = express.Router();

// Add cache headers middleware
const addCacheHeaders = (req, res, next) => {
  // Set cache headers with 10 minute expiry for products
  setCacheHeaders(res, 600);
  next();
};

// Cache invalidation middleware for product updates
const invalidateProductCache = (req, res, next) => {
  // Store the original send method
  const originalSend = res.send;

  // Override the send method
  res.send = function(body) {
    // If the request was successful, clear related caches
    if (res.statusCode >= 200 && res.statusCode < 300) {
      // Clear product-related caches
      const productId = req.params.id;
      const productSlug = req.body.slug || (body && JSON.parse(body).data?.slug);

      if (productId) {
        clearCache(`/api/products/id/${productId}`);
      }

      if (productSlug) {
        clearCache(`/api/products/${productSlug}`);
      }

      // Clear general product list and search caches
      clearCache('/api/products');
      clearCache('/api/search');

      console.log('Cleared product cache after update/delete');
    }

    // Call the original send method
    return originalSend.call(this, body);
  };

  next();
};

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - name
 *         - category
 *         - brand
 *         - image
 *         - description
 *         - opinion
 *         - score
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the product
 *         name:
 *           type: string
 *           description: Product name
 *         slug:
 *           type: string
 *           description: URL-friendly version of the name
 *         category:
 *           type: string
 *           description: Product category
 *           enum: [Electronics, Computers & Accessories, Smartphones & Accessories, Audio & Headphones, Cameras & Photography, Wearable Technology, Home & Kitchen, Furniture, Appliances, Home Decor, Bedding & Bath, Clothing & Fashion, Men's Clothing, Women's Clothing, Kids & Baby Clothing, Shoes, Jewelry & Watches, Bags & Accessories, Beauty & Personal Care, Makeup, Skincare, Hair Care, Fragrances, Health & Wellness, Sports & Outdoors, Exercise & Fitness, Outdoor Recreation, Sports Equipment, Toys & Games, Books & Media, Books, Movies & TV, Music, Video Games, Automotive, Office Supplies, Pet Supplies, Food & Beverages, Art & Crafts, Tools & Home Improvement, Garden & Outdoor, Travel & Luggage, Baby Products, Musical Instruments, Industrial & Scientific, Other]
 *         brand:
 *           type: string
 *           description: Product brand
 *         image:
 *           type: string
 *           description: URL to product image
 *         description:
 *           type: string
 *           description: Product description (up to 1000 characters)
 *           maxLength: 1000
 *         pros:
 *           type: array
 *           items:
 *             type: string
 *           description: List of product pros/advantages
 *         cons:
 *           type: array
 *           items:
 *             type: string
 *           description: List of product cons/disadvantages
 *         opinion:
 *           type: string
 *           description: Editorial opinion about the product
 *         score:
 *           type: number
 *           description: Product score (0-100)
 *           minimum: 0
 *           maximum: 100
 *         affiliateLink:
 *           type: string
 *           description: Affiliate link for the product
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date when the product was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date when the product was last updated
 */

/**
 * @swagger
 * tags:
 *   name: Products
 *   description: Product management API
 */

/**
 * @swagger
 * /products:
 *   get:
 *     summary: Get all products
 *     description: Retrieve a list of all products. Can be filtered, sorted, and paginated.
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by product category
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Filter by product brand
 *       - in: query
 *         name: select
 *         schema:
 *           type: string
 *         description: Fields to select (comma-separated)
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort fields (comma-separated, prefix with - for descending)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: A list of products
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                   description: Number of products returned
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     next:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                     prev:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                 total:
 *                   type: integer
 *                   description: Total number of products matching the query
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 */
router.get('/', cacheMiddleware(600), addCacheHeaders, getProducts);



/**
 * @swagger
 * /products/id/{id}:
 *   get:
 *     summary: Get product by ID
 *     description: Retrieve a single product by its MongoDB ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       404:
 *         description: Product not found
 */
router.get('/id/:id', cacheMiddleware(600), addCacheHeaders, getProductById);

/**
 * @swagger
 * /products/{slug}:
 *   get:
 *     summary: Get product by slug
 *     description: Retrieve a single product by its slug
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Product slug
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       404:
 *         description: Product not found
 */
router.get('/:slug', cacheMiddleware(600), addCacheHeaders, getProductBySlug);

/**
 * @swagger
 * /products:
 *   post:
 *     summary: Create a new product
 *     description: Create a new product (admin only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - category
 *               - brand
 *               - image
 *               - description
 *               - opinion
 *               - score
 *             properties:
 *               name:
 *                 type: string
 *                 description: Product name
 *               category:
 *                 type: string
 *                 description: Product category
 *               brand:
 *                 type: string
 *                 description: Product brand
 *               image:
 *                 type: string
 *                 description: URL to product image
 *               description:
 *                 type: string
 *                 description: Product description (up to 1000 characters)
 *                 maxLength: 1000
 *               pros:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of product pros/advantages
 *               cons:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of product cons/disadvantages
 *               opinion:
 *                 type: string
 *                 description: Editorial opinion about the product
 *               score:
 *                 type: number
 *                 description: Product score (0-100)
 *                 minimum: 0
 *                 maximum: 100
 *               affiliateLink:
 *                 type: string
 *                 description: Affiliate link for the product
 *     responses:
 *       201:
 *         description: Product created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       400:
 *         description: Invalid input or product already exists
 *       401:
 *         description: Not authorized to access this route
 *       403:
 *         description: User role not authorized to access this route
 */
router.post(
  '/',
  [
    protect,
    authorize('admin'),
    body('name', 'Name is required').not().isEmpty(),
    // Category is now optional for brand-based organization
    body('category').optional().not().isEmpty().withMessage('Category cannot be empty if provided'),
    body('subcategory').optional().not().isEmpty().withMessage('Subcategory cannot be empty if provided'),
    body('brand', 'Brand is required').not().isEmpty(),
    body('image', 'Image URL is required').not().isEmpty(),
    body('description', 'Description is required').not().isEmpty(),
    body('opinion', 'Opinion is required').not().isEmpty(),
    body('score', 'Score is required').not().isEmpty()
      .isNumeric().withMessage('Score must be a number')
      .custom(value => value >= 0 && value <= 100).withMessage('Score must be between 0 and 100')
  ],
  invalidateProductCache, // Add cache invalidation
  createProduct
);

/**
 * @swagger
 * /products/{id}:
 *   put:
 *     summary: Update a product
 *     description: Update an existing product (admin only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Product name
 *               category:
 *                 type: string
 *                 description: Product category
 *               brand:
 *                 type: string
 *                 description: Product brand
 *               image:
 *                 type: string
 *                 description: URL to product image
 *               description:
 *                 type: string
 *                 description: Product description (up to 1000 characters)
 *                 maxLength: 1000
 *               pros:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of product pros/advantages
 *               cons:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of product cons/disadvantages
 *               opinion:
 *                 type: string
 *                 description: Editorial opinion about the product
 *               score:
 *                 type: number
 *                 description: Product score (0-100)
 *                 minimum: 0
 *                 maximum: 100
 *               affiliateLink:
 *                 type: string
 *                 description: Affiliate link for the product
 *     responses:
 *       200:
 *         description: Product updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Not authorized to access this route
 *       403:
 *         description: User role not authorized to access this route
 *       404:
 *         description: Product not found
 */
router.put(
  '/:id',
  [
    protect,
    authorize('admin'),
    body('name', 'Name is required').optional().not().isEmpty(),
    // Category and subcategory are optional for brand-based organization
    body('category').optional().not().isEmpty().withMessage('Category cannot be empty if provided'),
    body('subcategory').optional().not().isEmpty().withMessage('Subcategory cannot be empty if provided'),
    body('brand', 'Brand is required').optional().not().isEmpty(),
    body('image', 'Image URL is required').optional().not().isEmpty(),
    body('description', 'Description is required').optional().not().isEmpty(),
    body('opinion', 'Opinion is required').optional().not().isEmpty(),
    body('score', 'Score is required').optional()
      .isNumeric().withMessage('Score must be a number')
      .custom(value => value >= 0 && value <= 100).withMessage('Score must be between 0 and 100')
  ],
  invalidateProductCache, // Add cache invalidation
  updateProduct
);

/**
 * @swagger
 * /products/{id}:
 *   delete:
 *     summary: Delete a product
 *     description: Delete a product (admin only)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: Empty object
 *       401:
 *         description: Not authorized to access this route
 *       403:
 *         description: User role not authorized to access this route
 *       404:
 *         description: Product not found
 */
router.delete('/:id',
  [protect, authorize('admin')],
  invalidateProductCache, // Add cache invalidation
  deleteProduct
);

module.exports = router;
